import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:icons_plus/icons_plus.dart';

class HeaderDialogAddPassword extends StatelessWidget {
  const HeaderDialogAddPassword({super.key, required this.theme});

  final ThemeData theme;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Icon(Bootstrap.plus_circle, color: theme.primaryColor, size: 24),
        15.horizontalSpace,
        Text(
          'Add New Password',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}


