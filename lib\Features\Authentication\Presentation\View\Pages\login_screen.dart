
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../Core/Utils/Components/show_message.dart';
import '../Widgets/form_login.dart';
import '../../../../../Config/Routes/app_routes.dart';
import '../../cubit/auth_cubit.dart';
import '../../../../../main.dart';

class LogInScreen extends StatelessWidget {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  LogInScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AuthCubit, AuthState>(
      listener: (context, state) {
       if (state.status != null) {
          if (state.status!) {
            showMessageScafold(context, message: state.message);
            kNavigationService.navigateTo(AppRoutes.home);
          } else {
            showMessageScafold(context, message: state.message);
          }
        }
      },
      builder: (context, state) {
        return Scaffold(
          body: Center(
            child: SafeArea(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(32.0),
                child: FormLogin(
                  formKey: _formKey,
                  emailController: _emailController,
                  passwordController: _passwordController,
                  state: state,
                ),
              ),
            ),
          ),
          resizeToAvoidBottomInset: true,
        );
      },
    );
  }
}
