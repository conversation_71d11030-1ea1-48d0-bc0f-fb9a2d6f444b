import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:flutter/material.dart';
import 'package:icons_plus/icons_plus.dart';
import 'package:keyper/Core/Resources/app_colors.dart';
import '../../../Resources/strings.dart';
import '../../Validator/validate.dart';

import '../../../Resources/app_icons.dart';
import 'PasswordVisibility/password_visibility_cubit.dart';
import 'app_text_form_field.dart';

class CustomPasswordTextFromField extends StatelessWidget {
  final TextEditingController controller;
  final String fieldId; // Add a unique ID for each field
  final bool showForgetMessage;
  final bool isLogin;
  final bool isAddItem;
  final VoidCallback? actionIcon;

  final String hintText;

  const CustomPasswordTextFromField({
    super.key,
    required this.controller,
    required this.fieldId,
    this.showForgetMessage = true,
    required this.hintText,
    this.isLogin = true,
    this.isAddItem = false,
    this.actionIcon,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => PasswordVisibilityCubit(),
      child: BlocBuilder<PasswordVisibilityCubit, Map<String, bool>>(
        builder: (context, state) {
          final isVisible = state[fieldId] ?? false;
          return AppTextFormField(
            suffixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                isAddItem
                    ? IconButton(
                      icon: Icon(
                        Bootstrap.magic,
                        color: AppColors.primary,
                        size: 20,
                      ),
                      tooltip: 'Generate strong password',
                      onPressed: actionIcon,
                    )
                    : SizedBox(),

                IconButton(
                  icon:
                      isVisible
                          ? AppIcons.passwordVisibility
                          : AppIcons.passwordVisibilityOff,
                  onPressed: () {
                    context
                        .read<PasswordVisibilityCubit>()
                        .togglePasswordVisibility(fieldId);
                  },
                ),
              ],
            ),
            controller: controller,
            hintText: "",
            keyboardType: TextInputType.visiblePassword,
            labelText: AppStrings.password,
            prefixIcon:
                isAddItem
                    ? IconButton(
                      icon: Icon(
                        Bootstrap.magic,
                        color: AppColors.primary,
                        size: 20,
                      ),
                      tooltip: 'Generate strong password',
                      onPressed: actionIcon,
                    )
                    : null,
            obscureText: !isVisible,

            validator: (p0) => validatePassword(!isLogin, password: p0 ?? ""),
          );
        },
      ),
    );
  }
}
