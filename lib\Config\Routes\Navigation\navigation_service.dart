import 'package:flutter/material.dart';
import 'navigation_contract.dart';

class AppNavigationService implements NavigationContract {
  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  @override
  Future<void> navigateTo(String route, {Object? arguments}) async {
    await navigatorKey.currentState?.pushNamed(route, arguments: arguments);
  }

  @override
  Future<void> replaceWith(String route, {Object? arguments}) async {
    await navigatorKey.currentState?.pushReplacementNamed(route, arguments: arguments);
  }

  @override
  Future<void> clearAndNavigateTo(String route, {Object? arguments}) async {
    await navigatorKey.currentState?.pushNamedAndRemoveUntil(
      route,
          (Route<dynamic> route) => false,
      arguments: arguments,
    );
  }

  @override
  void goBack([Object? result]) {
    if (navigatorKey.currentState?.canPop() ?? false) {
      navigatorKey.currentState?.pop(result);
    }
  }
}
