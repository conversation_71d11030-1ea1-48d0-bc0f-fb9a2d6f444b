import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:keyper/Features/Mian/cubit/main_cubit.dart';
import 'Config/Cubit/settings_cubit.dart';
import 'Core/Storage/Local/local_storage_service.dart';

import 'Config/Routes/Navigation/navigation_service.dart';
import 'Config/Routes/route_generator.dart';
import 'Config/Themes/dark_themes.dart';
import 'Config/Themes/light_themes.dart';
import 'Config/constants.dart';
import 'Config/firebase_options.dart';
import 'Core/Storage/Firebase/firebase_service.dart';
import 'Features/Authentication/Presentation/cubit/auth_cubit.dart';
import 'Features/Profile/Presentation/cubit/profile_cubit.dart';
import 'package:hive_flutter/adapters.dart';

final AppNavigationService kNavigationService = AppNavigationService();

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Hive.initFlutter();
  await LocalStorageService.init();
  // await LocalStorageService.clear();

  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Initialize Firebase App Check with error handling
  try {
    await FirebaseAppCheck.instance.activate(
      // Use debug provider for development
      androidProvider: AndroidProvider.debug,
      // Use appropriate provider for production
      // androidProvider: AndroidProvider.playIntegrity,
      // For iOS (when you add iOS support)
      // iosProvider: AppleProvider.debug,
    );
    debugPrint('Firebase App Check activated successfully');
  } catch (e) {
    debugPrint('Firebase App Check activation failed: $e');
    // Continue app execution even if App Check fails
  }

  FirebaseServices.instance.initialize();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => SettingsCubit()),
        BlocProvider(create: (context) => AuthCubit()),
        BlocProvider(create: (context) => ProfileCubit()..init()),
        BlocProvider(create: (context) => MainCubit()..init()),
      ],
      child: ScreenUtilInit(
        designSize: const Size(375, 812),
        minTextAdapt: true,
        splitScreenMode: true,
        useInheritedMediaQuery: true,
        enableScaleWH: () => false,
        enableScaleText: () => true,
        builder: (p0, child) {
          ScreenUtil.init(p0);
          return BlocBuilder<SettingsCubit, SettingsState>(
            builder: (context, state) {
              return MaterialApp(
                title: AppConstants.appName,
                debugShowCheckedModeBanner: false,
                theme: themeLightData(),
                darkTheme: themeDarkData(),
                themeMode: state.themeMode,
                onGenerateRoute: RouteGenerator.generateRoute,
                initialRoute: context.read<SettingsCubit>().getRoute(),
                navigatorKey: kNavigationService.navigatorKey,
              );
            },
          );
        },
      ),
    );
  }
}
