


import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

import '../../../../../Core/Storage/Firebase/firebase_service.dart';
import '../../../../Core/Storage/Firebase/response_model.dart';
import '../../../../Core/Storage/Local/local_storage_keys.dart';
import '../../../../Core/Storage/Local/local_storage_service.dart';
import '../../Data/Models/create_account.dart';
import '../../Data/Models/login_model.dart';
part 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> {
  AuthCubit() : super(AuthState());

  Future<void> login(LoginModel loginModel) async {
    emit(state.copyWith(loadingLogin: true));
    final response = await FirebaseServices.instance.loginAccount(
      loginModel.emial,
      loginModel.password,
    );
    // log("message: ${response.data}");
    await saveLogin(response);
    final message = response.status? response.message: response.failure?.message;
    emit(
      state.copyWith(
        loadingLogin: false,
        statusLogin: response.status,
        message: message,
      ),
    );
  }

  Future<void> createAccount(CreateAccountModel account) async {
    emit(state.copyWith(loadingCreateAccount: true));
    final response = await FirebaseServices.instance.createAccount(account);
    // log("message final : ${response.message}");
    await saveLogin(response);
    emit(
      state.copyWith(
        loadingCreateAccount: false,
        statusSingUp: response.status,
        message: response.message,
      ),
    );
  }

  Future<void> logout() async {
    await LocalStorageService.setValue(LocalStorageKeys.isLoggedIn, false);
    await LocalStorageService.removeValue(LocalStorageKeys.setData);
  }

  Future<void> saveLogin(ResponseModel response) async {
    if (response.status) {
      await LocalStorageService.setValue(LocalStorageKeys.isLoggedIn, true);

      await LocalStorageService.setValue(
        LocalStorageKeys.setData,
        response.data.toString(),
      );

      await LocalStorageService.setValue(
        LocalStorageKeys.token,
        response.data.toString(),
      );
    }
  }
}
