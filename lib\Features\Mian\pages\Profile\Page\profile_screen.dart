import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../Widget/build_account_verification_banner.dart';
import '../Widget/build_profile_header_with_photo.dart';
import '../Widget/build_profile_information_section.dart';

import '../../../cubit/main_cubit.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final user = context.read<MainCubit>().state.user;

    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
             
              BuildAccountVerificationBanner(theme: theme, isVerifphone: user?.isPhoneVerified??false, isVerifEmail: user?.isEmailVerified??false,),
              BuildProfileHeaderWithPhoto(theme: theme, user: user),
              BuildProfileInformationSection(user:user! , theme: theme,),
             
            ],
          ),
        ),
      ),
    );
  }
}
