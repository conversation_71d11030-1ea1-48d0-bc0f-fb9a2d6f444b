import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../../../../../Core/Utils/Components/TextField/app_text_form_field.dart';
import '../../../../../../../../../Core/Utils/Components/TextField/password_text__form_field.dart';
import '../../../../../../../../../Core/Utils/Components/build_button_app.dart';
import '../../../../../../../../../Core/Utils/Components/build_text.dart';
import '../../../../../../../../../Core/Resources/fonts.dart';
import '../../../../../../../../../Core/Resources/strings.dart';
import '../../../../../../../../../Core/Utils/Validator/validate.dart';
import '../../../../../../../../../main.dart';
import '../../../Data/Models/create_account.dart';
import '../../cubit/auth_cubit.dart';

class FormCreateAccounts extends StatelessWidget {
  const FormCreateAccounts({
    super.key,
    required GlobalKey<FormState> formKey,
    required TextEditingController nameController,
    required TextEditingController emailController,
    required TextEditingController phoneController,
    required TextEditingController passwordController,
    required this.state,
  }) : _formKey = formKey,
       _nameController = nameController,
       _emailController = emailController,
       _phoneController = phoneController,
       _passwordController = passwordController;

  final GlobalKey<FormState> _formKey;
  final TextEditingController _nameController;
  final TextEditingController _emailController;
  final TextEditingController _phoneController;
  final TextEditingController _passwordController;
  final AuthState state;

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        spacing: 40.h,
        children: [
          buildText(
            text: AppStrings.welcome,
            textAlign: TextAlign.center,
            style: AppTextStyles.bodyLargeMedium(),
          ),

          AppTextFormField(
            controller: _nameController,
            labelText: AppStrings.name,
            keyboardType: TextInputType.name,
            validator: (value) => customValidate(value: value ?? ""),
            textInputAction: TextInputAction.next,
          ),
          AppTextFormField(
            controller: _emailController,
            labelText: AppStrings.email,
            hintText: AppStrings.emailHint,
            keyboardType: TextInputType.emailAddress,
            validator: (value) => validateEmail(email: value ?? ""),
            textInputAction: TextInputAction.next,
          ),
          AppTextFormField(
            controller: _phoneController,
            labelText: AppStrings.phoneNumber,
            keyboardType: TextInputType.phone,
            validator: (value) => validatePhone(value ?? ""),
            textInputAction: TextInputAction.next,
          ),
          CustomPasswordTextFromField(
            controller: _passwordController,
            hintText: AppStrings.passwordHint,
            fieldId: "CreatePassword",
            isLogin: false,
          ),
          BuildButtonApp(
            onPressed: () {
              if (_formKey.currentState!.validate()) {
                final account = CreateAccountModel(
                  email: _emailController.text,
                  password: _passwordController.text,
                  name: _nameController.text,
                  phoneNumber: _phoneController.text,
                );
                context.read<AuthCubit>().createAccount(account);
              }
            },
            text: AppStrings.createAccount,
          ),

          TextButton(
            onPressed: () => kNavigationService.goBack(),
            child: buildText(
              text: AppStrings.backToLogin,
              style: AppTextStyles.bodyLargeMedium(),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
