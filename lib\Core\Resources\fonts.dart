import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

import 'app_colors.dart';

class AppTextStyles {
  static TextStyle _getTextStyle(
    double fontSize,
    FontWeight fontWeight, {
    Color? color,
  }) {
    return GoogleFonts.alata(
      fontSize: fontSize.sp,
      fontWeight: fontWeight,
      color: color ?? AppColors.textPrimary,
    ).copyWith(overflow: TextOverflow.ellipsis);
  } //* Headings

  // static TextStyle h1Bold({Color? color}) => _getTextStyle(48, FontWeight.bold, color: color);
  // static TextStyle h2Bold({Color? color}) => _getTextStyle(40, FontWeight.bold, color: color);
  static TextStyle h3Bold({Color? color}) =>
      _getTextStyle(32, FontWeight.bold, color: color);
  static TextStyle h4Bold({Color? color}) =>
      _getTextStyle(24, FontWeight.bold, color: color); // 2 w , b
  static TextStyle h5Bold({Color? color}) =>
      _getTextStyle(20, FontWeight.bold, color: color);
  static TextStyle h6Bold({Color? color}) =>
      _getTextStyle(18, FontWeight.bold, color: color); //
  static TextStyle h7Bold({Color? color}) =>
      _getTextStyle(14, FontWeight.bold, color: color);
  // static TextStyle h1SemiBold({Color? color}) => _getTextStyle(48, FontWeight.w600, color: color);
  // static TextStyle h2SemiBold({Color? color}) => _getTextStyle(40, FontWeight.w600, color: color);
  // static TextStyle h3SemiBold({Color? color}) => _getTextStyle(32, FontWeight.w600, color: color);
  // static TextStyle h4SemiBold({Color? color}) => _getTextStyle(24, FontWeight.w600, color: color);
  // static TextStyle h5SemiBold({Color? color}) =>
  //     _getTextStyle(20, FontWeight.w600, color: color);
  // static TextStyle h6SemiBold({Color? color}) =>
  //     _getTextStyle(18, FontWeight.w600, color: color);
  // static TextStyle h1Medium({Color? color}) => _getTextStyle(48, FontWeight.w500, color: color);
  // static TextStyle h2Medium({Color? color}) => _getTextStyle(40, FontWeight.w500, color: color);
  // static TextStyle h3Medium({Color? color}) => _getTextStyle(32, FontWeight.w500, color: color);
  // static TextStyle h4Medium({Color? color}) => _getTextStyle(24, FontWeight.w500, color: color);
  // static TextStyle h5Medium({Color? color}) => _getTextStyle(20, FontWeight.w500, color: color);
  // static TextStyle h6Medium({Color? color}) =>
  //     _getTextStyle(18, FontWeight.bold, color: color);
  // static TextStyle h7Medium({Color? color}) =>
  //     _getTextStyle(14, FontWeight.w500, color: color);

  // //* Body Text Styles
  // static TextStyle bodyXtraLargeBold({Color? color}) =>
  //     _getTextStyle(18, FontWeight.bold, color: color);
  // static TextStyle bodyLargeBold({Color? color}) =>
  //     _getTextStyle(16, FontWeight.bold, color: color);
  // static TextStyle bodyMediumBold({Color? color}) =>
  //     _getTextStyle(14, FontWeight.bold, color: color);
  // static TextStyle bodySmallBold({Color? color}) =>
  //     _getTextStyle(12, FontWeight.bold, color: color);
  // static TextStyle bodyXtraSmallBold({Color? color}) =>
  //     _getTextStyle(10, FontWeight.bold, color: color);
  // static TextStyle bodyXtraLargeSemiBold({Color? color}) => _getTextStyle(18, FontWeight.w600, color: color);
  static TextStyle bodyLargeSemiBold({Color? color}) =>
      _getTextStyle(16, FontWeight.w600, color: color);
  // static TextStyle bodyMediumSemiBold({Color? color}) =>
  //     _getTextStyle(14, FontWeight.w600, color: color);
  static TextStyle bodySmallSemiBold({Color? color}) =>
      _getTextStyle(12, FontWeight.w600, color: color);
  // static TextStyle bodyXtraSmallSemiBold({Color? color}) =>
  //     _getTextStyle(10, FontWeight.w600, color: color);
  // static TextStyle bodyXtraLargeMedium({Color? color}) =>
  //     _getTextStyle(18, FontWeight.w500, color: color);
  static TextStyle bodyLargeMedium({Color? color}) =>
      _getTextStyle(16, FontWeight.w500, color: color);
  static TextStyle bodyMediumMedium({Color? color}) =>
      _getTextStyle(14, FontWeight.w500, color: color);
  static TextStyle bodySmallMedium({Color? color}) => _getTextStyle(12, FontWeight.w500, color: color);
  static TextStyle bodyXtraSmallMedium({Color? color}) =>
      _getTextStyle(10, FontWeight.w500, color: color);

  // Label Text Styles
  static TextStyle labelLarge({Color? color}) =>
      _getTextStyle(14, FontWeight.w500, color: color);
  static TextStyle labelMedium({Color? color}) =>
      _getTextStyle(12, FontWeight.w500, color: color);
  static TextStyle labelSmall({Color? color}) =>
      _getTextStyle(10, FontWeight.w500, color: color);

  // Display Text Styles
  static TextStyle displayLarge({Color? color}) =>
      _getTextStyle(57, FontWeight.w400, color: color);
  static TextStyle displayMedium({Color? color}) =>
      _getTextStyle(45, FontWeight.w400, color: color);
  static TextStyle displaySmall({Color? color}) =>
      _getTextStyle(36, FontWeight.w400, color: color);

  // Title Text Styles
  static TextStyle titleLarge({Color? color}) =>
      _getTextStyle(22, FontWeight.w500, color: color);
  static TextStyle titleMedium({Color? color}) =>
      _getTextStyle(16, FontWeight.w500, color: color);
  static TextStyle titleSmall({Color? color}) =>
      _getTextStyle(14, FontWeight.w500, color: color);
}
