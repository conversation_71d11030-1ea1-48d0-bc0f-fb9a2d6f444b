import '../Storage/Firebase/keys_data.dart';

class ItemsAccountsModel {
  final String title;
  final String password;
  final String icon;
  final String idItem;
  ItemsAccountsModel({
    required this.title,
    required this.password,
    required this.icon,
    required this.idItem,
  });

  Map<String, dynamic> to<PERSON>son() {
    return {
      AppKeysData.title: title,
      AppKeysData.password: password,
      AppKeysData.icon: icon,
      AppKeysData.idItem: idItem,
    };
  }
  factory ItemsAccountsModel.fromJson(Map<String, dynamic> json) {
    return ItemsAccountsModel(
      title: json[AppKeysData.title],
      password: json[AppKeysData.password],
      icon: json[AppKeysData.icon],
      idItem: json[AppKeysData.idItem],
    );
  }
}
