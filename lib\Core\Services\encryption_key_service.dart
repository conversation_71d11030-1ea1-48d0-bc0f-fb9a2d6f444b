
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:flutter/foundation.dart';

import '../Storage/Local/local_storage_service.dart';

enum EncryptionMethod {
  aes256,
  fernet,
}

extension EncryptionMethodExtension on EncryptionMethod {
  String get displayName {
    switch (this) {
      case EncryptionMethod.aes256:
        return 'AES-256 (معيار التشفير المتقدم)';
      case EncryptionMethod.fernet:
        return 'Fernet (تشفير متماثل مع توقيع وطابع زمني)';
    }
  }

  String get hint {
    switch (this) {
      case EncryptionMethod.aes256:
        return 'أكثر طرق التشفير استخداماً وأماناً عالمياً، مناسب لمعظم الاستخدامات.';
      case EncryptionMethod.fernet:
        return 'سهل الاستخدام ويوفر حماية إضافية ضد التلاعب بالبيانات ويزيد من أمان البيانات.';
    }
  }
}


class EncryptionService {
  // مفاتيح التشفير الأساسية اللي هنخزنها عبر LocalStorageService
  static const String _aesKeyStorageKey = 'app_encryption_aes_key';
  static const String _aesIvStorageKey = 'app_encryption_aes_iv';
  static const String _fernetKeyStorageKey = 'app_encryption_fernet_key';

  // Instance لمفاتيح التشفير بعد استرجاعها من التخزين
  encrypt.Key? _aesKeyInstance;
  encrypt.IV? _aesIvInstance;
  encrypt.Key? _fernetKeyInstance;

  // وظيفة لتهيئة مفاتيح التشفير: توليدها مرة واحدة وتخزينها في LocalStorageService
  Future<void> _ensureEncryptionKeysExist() async {
    // تم حذف boxName: 'appConfigBox'
    if (await LocalStorageService.getValue(_aesKeyStorageKey) == null) {
      final aesKey = encrypt.Key.fromSecureRandom(32); // 256 بت
      final aesIv = encrypt.IV.fromSecureRandom(16); // 128 بت
      final fernetKey = encrypt.Key.fromSecureRandom(32); // 256 بت

      await LocalStorageService.setValue(_aesKeyStorageKey, aesKey.base64);
      await LocalStorageService.setValue(_aesIvStorageKey, aesIv.base64);
      await LocalStorageService.setValue(_fernetKeyStorageKey, fernetKey.base64);

      debugPrint('Generated and stored new encryption keys via LocalStorageService');
    } else {
      _aesKeyInstance = encrypt.Key.fromBase64(await LocalStorageService.getValue(_aesKeyStorageKey) as String);
      _aesIvInstance = encrypt.IV.fromBase64(await LocalStorageService.getValue(_aesIvStorageKey) as String);
      _fernetKeyInstance = encrypt.Key.fromBase64(await LocalStorageService.getValue(_fernetKeyStorageKey) as String);
    }
  }

  Future<encrypt.Key> _getAesKey() async {
    if (_aesKeyInstance == null) await _ensureEncryptionKeysExist();
    return _aesKeyInstance!;
  }

  Future<encrypt.IV> _getAesIv() async {
    if (_aesIvInstance == null) await _ensureEncryptionKeysExist();
    return _aesIvInstance!;
  }

  Future<encrypt.Key> _getFernetKey() async {
    if (_fernetKeyInstance == null) await _ensureEncryptionKeysExist();
    return _fernetKeyInstance!;
  }

  Future<String> encryptKey({
    required String userKey,
    required EncryptionMethod method,
  }) async {
    await _ensureEncryptionKeysExist();

    final encrypt.Encrypter encrypter;

    switch (method) {
      case EncryptionMethod.aes256:
        final aesKey = await _getAesKey();
        final aesIv = await _getAesIv();
        encrypter = encrypt.Encrypter(
          encrypt.AES(aesKey, mode: encrypt.AESMode.cbc, padding: 'PKCS7'),
        );
        return encrypter.encrypt(userKey, iv: aesIv).base64;
      case EncryptionMethod.fernet:
        final fernetKey = await _getFernetKey();
        encrypter = encrypt.Encrypter(encrypt.Fernet(fernetKey));
        return encrypter.encrypt(userKey).base64;
    }
  }

  Future<String> decryptKey({
    required String encryptedUserKeyBase64,
    required EncryptionMethod method,
  }) async {
    await _ensureEncryptionKeysExist();

    final encrypt.Encrypter encrypter;
    final encrypted = encrypt.Encrypted.fromBase64(encryptedUserKeyBase64);

    try {
      switch (method) {
        case EncryptionMethod.aes256:
          final aesKey = await _getAesKey();
          final aesIv = await _getAesIv();
          encrypter = encrypt.Encrypter(
            encrypt.AES(aesKey, mode: encrypt.AESMode.cbc, padding: 'PKCS7'),
          );
          return encrypter.decrypt(encrypted, iv: aesIv);
        case EncryptionMethod.fernet:
          final fernetKey = await _getFernetKey();
          encrypter = encrypt.Encrypter(encrypt.Fernet(fernetKey));
          return encrypter.decrypt(encrypted);
      }
    } catch (e) {
      debugPrint('Error decrypting: $e');
      rethrow;
    }
  }

  Future<void> saveEncryptedUserKey({
    required String encryptedKeyBase64,
    required EncryptionMethod method,
  }) async {
    // تم حذف boxName: 'userKeyBox'
    await LocalStorageService.setValue('encrypted_user_key', encryptedKeyBase64);
    await LocalStorageService.setValue('encryption_method', method.name);
    debugPrint('Encrypted User Key saved via LocalStorageService (default box) using ${method.name}.');
  }

  Future<Map<String, String?>?> getEncryptedUserKeyAndMethod() async {
    // تم حذف boxName: 'userKeyBox'
    final encryptedKey = await LocalStorageService.getValue('encrypted_user_key');
    final methodString = await LocalStorageService.getValue('encryption_method');
    if (encryptedKey != null && methodString != null) {
      return {
        'key': encryptedKey,
        'method': methodString,
      };
    }
    return null;
  }

  // ----------- وظائف معلومات الجهاز الفريدة ----------------
  Future<String?> getUniqueDeviceId() async {
    if (kIsWeb) {
      return 'web_device_id';
    }
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      return androidInfo.id;
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      return iosInfo.identifierForVendor;
    }
    return null;
  }

  // ----------- وظائف تسجيل الأحداث الأمنية ----------------
  Future<void> logSecurityEvent(String eventType, String description) async {
    final deviceId = await getUniqueDeviceId() ?? 'unknown_device';
    final logEntry = {
      'timestamp': DateTime.now().toIso8601String(),
      'eventType': eventType,
      'description': description,
      'deviceId': deviceId,
    };
    // Hive logs box (يمكن تخزينها كقائمة أو كل سجل بمفتاح فريد)
    // تم حذف boxName: 'securityLogsBox'
    final currentLogs = LocalStorageService.getValue('security_logs') ?? [];
    if (currentLogs is List) {
      currentLogs.add(logEntry);
      await LocalStorageService.setValue('security_logs', currentLogs);
    } else {
      await LocalStorageService.setValue('security_logs', [logEntry]);
    }
    debugPrint('Security Logged: $logEntry');
  }

  Future<List<dynamic>> getSecurityLogs() async {
    return LocalStorageService.getValue('security_logs') ?? [];
  }
}
