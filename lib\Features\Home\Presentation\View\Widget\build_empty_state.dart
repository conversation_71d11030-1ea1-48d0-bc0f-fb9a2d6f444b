import 'package:flutter/material.dart';
import 'package:icons_plus/icons_plus.dart';

class BuildEmptyState extends StatelessWidget {
  const BuildEmptyState({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Bootstrap.unlock, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No passwords yet',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tap the + button to add your first password',
            style: TextStyle(color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }
}
