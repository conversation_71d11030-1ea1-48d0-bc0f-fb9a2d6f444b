//***Password
String? validatePassword(bool singIn, {required String password}) {
  if (password.isEmpty) {
    return "Please Enter Password";
  }
  //Password must contain at least 8 characters
  else if (singIn) {
    if (password.length < 8) {
      return 'The password must be at least 8 characters long.';
    }
    // must contain at least one uppercase letter
    else if (!password.contains(RegExp(r'[A-Z]'))) {
      return 'The password must contain at least one uppercase letter.';
    }
    // Password must contain at least one lowercase letter
    else if (!password.contains(RegExp(r'[a-z]'))) {
      return 'The password must contain at least one lowercase letter.';
    }
    // Password must contain at least one digit
    else if (!password.contains(RegExp(r'[0-9]'))) {
      return 'The password must contain at least one digit.';
    }
    // Password must not contain only numbers
    else if (RegExp(r'^[0-9]+$').hasMatch(password)) {
      return 'The password must not contain only numbers.';
    }
  }
  return null;
}

//***Phone
String? validatePhone(String phone) {
  const String pattern = r'(^(?:[+0]9)?[0-9]{10,12}$)';
  final RegExp regex = RegExp(pattern);
  // Asynchronous Firebase Firestore check for existing phone number
  // final FirebaseFirestore firestore = FirebaseFirestore.instance;
  // final CollectionReference usersCollection = firestore.collection('Users');
  // final QuerySnapshot querySnapshot =
  //     await usersCollection.where('phone', isEqualTo: phone).get();
  if (phone.isEmpty) {
    return "Please Enter Your Phone";
  }
  // else  if (phone.length>=10 && phone.length<=13) {
  //   return '';
  // }
  else if (!regex.hasMatch(phone)) {
    return 'The phone number is invalid. Please enter a valid 10-12 digit phone number.';
  }
  // else if (querySnapshot.docs.isNotEmpty) {
  //   return 'The phone number is already registered.';
  // }
  return null;
}

//***Email
String? validateEmail({required String email}) {
  const String pattern = r'^[\w-]+(\.[\w-]+)*@([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,7}$';
  final RegExp regex = RegExp(pattern);

  //------------------------
  if (email.isEmpty) {
    return "Please Enter your Email ";
  } else if (!regex.hasMatch(email)) {
    return 'The email address is badly formatted.';
  }
  return null;
}

//*** Custom Validate
String? customValidate({
  required String value,
  String? titleCheck,
  bool trimWhitespace = true,
  int? minLength,
  int? maxLength,
  String? pattern,
  String? emptyMessage,
  String? minLengthMessage,
  String? maxLengthMessage,
  String? invalidPatternMessage,
}) {
  // Assigning default field name
  final String p0 = titleCheck ?? "Field";

  // Trim leading/trailing whitespace if trimWhitespace is true
  final String trimmedValue = trimWhitespace ? value.trim() : value;

  // Check if value is empty
  if (minLength == null && trimmedValue.isEmpty) {
    return emptyMessage ?? "Please enter this $p0.";
  }

  // Check for minimum length
  if (minLength != null && trimmedValue.length < minLength) {
    return minLengthMessage ??
        "$p0 must be at least $minLength characters long.";
  }

  // Check for maximum length
  if (maxLength != null && trimmedValue.length > maxLength) {
    return maxLengthMessage ??
        "$p0 must be at most $maxLength characters long.";
  }

  // Check for pattern (regex) validation
  if (pattern != null) {
    final regex = RegExp(pattern);
    if (!regex.hasMatch(trimmedValue)) {
      return invalidPatternMessage ?? "$p0 format is invalid.";
    }
  }

  // If all checks pass, return null (no errors)
  return null;
}

