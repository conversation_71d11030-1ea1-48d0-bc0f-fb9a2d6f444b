part of 'main_cubit.dart';

@immutable
 class MainState {
  final List<ItemsModel>? items;
  final UserModel? user;
  final int index ;
  final String? passwordStrength;
  final IconData selectedIcon;

  const MainState({
     this.items=const[],
     this.passwordStrength,
     this.index=0,
     this.user,
     this.selectedIcon=Bootstrap.lock,
  });

  MainState copyWith({
    List<ItemsModel>? items,
    String? passwordStrength,
    IconData? selectedIcon,
    int? index,
    UserModel? user,
  }) {
    return MainState(
      items: items ?? this.items,
      passwordStrength: passwordStrength ?? this.passwordStrength,
      selectedIcon: selectedIcon ?? this.selectedIcon,
      index: index ?? this.index,
      user: user ?? this.user,
    );
  }
 }

