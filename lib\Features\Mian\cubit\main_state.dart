part of 'main_cubit.dart';

@immutable
class MainState {
  final List<ItemsModel>? items;
  final UserModel? user;
  final int index;
  final String? passwordStrength;
  final IconData selectedIcon;
  final bool? loadingDataUSer;
  final bool? loadingaddItemData;
  final bool? loadingRemoveItemData;
  final String? messagesAddItemData;
  final String? messagesRemoveItemData;

  const MainState({
    this.items = const [],
    this.passwordStrength,
    this.index = 0,
    this.user,
    this.loadingDataUSer = false, // ابدأ بـ false بدلاً من null
    this.loadingRemoveItemData,
    this.loadingaddItemData,
    this.messagesAddItemData,
    this.messagesRemoveItemData,
    this.selectedIcon = Bootstrap.lock,
  });

  MainState copyWith({
    List<ItemsModel>? items,
    String? passwordStrength,
    IconData? selectedIcon,
    int? index,
    UserModel? user,
    bool? loadingDataUSer,
    bool? loadingaddItemData,
    bool? loadingRemoveItemData,
    String? messagesAddItemData,
    String? messagesRemoveItemData,
  }) {
    return MainState(
      items: items ?? this.items,
      passwordStrength: passwordStrength ?? this.passwordStrength,
      selectedIcon: selectedIcon ?? this.selectedIcon,
      index: index ?? this.index,
      user: user ?? this.user,
      loadingDataUSer: loadingDataUSer ?? this.loadingDataUSer,
      loadingaddItemData: loadingaddItemData ?? this.loadingaddItemData,
      messagesAddItemData: messagesAddItemData ?? this.messagesAddItemData,
      messagesRemoveItemData:
          messagesRemoveItemData ?? this.messagesRemoveItemData,
    );
  }
}
