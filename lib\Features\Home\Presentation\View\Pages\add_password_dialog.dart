import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:icons_plus/icons_plus.dart';
import 'package:keyper/Core/Utils/Components/TextField/app_text_form_field.dart';
import 'package:keyper/Core/Utils/Components/build_button_app.dart';
import 'package:keyper/Core/Utils/Components/build_text.dart';
import 'package:keyper/Core/Utils/Validator/validate.dart';
import 'package:keyper/Features/Home/Presentation/View/Widget/build_box_show_icons.dart';
import 'package:keyper/Features/Home/Presentation/View/Widget/header_dialog_add_password.dart';
import 'package:keyper/Features/Home/Data/Model/items_model.dart';
import 'package:keyper/Features/Mian/cubit/main_cubit.dart';
import 'package:keyper/main.dart';

import '../../../../../Core/Resources/generate_password.dart';
import '../../../../../Core/Utils/Components/TextField/password_text__form_field.dart';

class AddPasswordDialog extends StatefulWidget {
  const AddPasswordDialog({super.key});

  @override
  State<AddPasswordDialog> createState() => _AddPasswordDialogState();
}

class _AddPasswordDialogState extends State<AddPasswordDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  void initState() {
    super.initState();

    _passwordController.addListener(() {
      context.read<MainCubit>().listenForPasswordChanges(
        _passwordController.text,
      );
    });
  }

  @override
  void dispose() {
    _titleController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _generateRandomPassword() {
    final password = GeneratePassword.generateStrongPassword();
    _passwordController.text = password;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BlocBuilder<MainCubit, MainState>(
      builder: (context, state) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 0,
          backgroundColor: Colors.transparent,

          child: Container(
            padding: const EdgeInsets.all(20),
            height: 430.h,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(20),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Form(
              key: _formKey,
              child: ListView(
                children: [
                  HeaderDialogAddPassword(theme: theme),
                  24.verticalSpace,
                  buildText(
                    text: 'Choose Icon',
                    style: theme.textTheme.titleSmall!,
                  ),
                  8.verticalSpace,
                  SizedBox(
                    height: 56.h,
                    child: ListView(
                      scrollDirection: Axis.horizontal,
                      children:
                          ItemsModel.availableIcons.map((icon) {
                            final isSelected = icon == state.selectedIcon;
                            return BuildBoxShowIcons(
                              isSelected: isSelected,
                              theme: theme,
                              icon: icon,
                            );
                          }).toList(),
                    ),
                  ),
                  16.verticalSpace,

                  AppTextFormField(
                    controller: _titleController,
                    hintText: 'e.g. Facebook, Gmail, Bank Account',
                    keyboardType: TextInputType.name,
                    labelText: 'Title',

                    prefixIcon: const Icon(Bootstrap.type),
                    validator: (value) => customValidate(value: value ?? ""),
                  ),

                  16.verticalSpace ,

                  // Password field
                  CustomPasswordTextFromField(
                    controller: _passwordController,
                    isLogin: true,
                    fieldId: "AddItem",
                    hintText: '••••••••',
                    isAddItem: true,
                    actionIcon: _generateRandomPassword,
                  ),
                  20.verticalSpace ,

                  // Password strength indicator
                  if (state.passwordStrength != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0, left: 4.0),
                      child: Row(
                        children: [
                          Icon(
                            state.passwordStrength == 'Strong'
                                ? Bootstrap.shield_check
                                : state.passwordStrength == 'Medium'
                                ? Bootstrap.shield_exclamation
                                : Bootstrap.shield_x,
                            size: 16,
                            color:
                                state.passwordStrength == 'Strong'
                                    ? Colors.green
                                    : state.passwordStrength == 'Medium'
                                    ? Colors.orange
                                    : Colors.red,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            '${state.passwordStrength} password',
                            style: TextStyle(
                              fontSize: 12,
                              color:
                                  state.passwordStrength == 'Strong'
                                      ? Colors.green
                                      : state.passwordStrength == 'Medium'
                                      ? Colors.orange
                                      : Colors.red,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),

                  // Action buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: Text(
                          'Cancel',
                          style: TextStyle(color: Colors.grey[700]),
                        ),
                      ),
                      8.horizontalSpace,
                      BuildButtonApp(
                        onPressed: () {
                          if (_formKey.currentState!.validate()) {
                            final newItem = ItemsModel(
                              title: _titleController.text,
                              password: _passwordController.text,
                              icon: state.selectedIcon,
                              idItem:
                                  DateTime.now().millisecondsSinceEpoch
                                      .toString(),
                            );

                            // context.read<MainState>().addItem(newItem);

                            kNavigationService.goBack(newItem);
                          }
                        },
                        text: "Save",
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
