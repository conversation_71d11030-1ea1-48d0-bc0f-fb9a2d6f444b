import 'dart:math';

class GeneratePassword {
    /// Generates a strong random password
  /// Evaluates password strength and updates the strength indicator
  static String evaluatePasswordStrength(String password) {
    String passwordStrength = '';
    if (password.isEmpty) {
      return passwordStrength;
    }

    int score = 0;

    // Length check
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;
    if (password.length >= 16) score++;

    // Character variety check
    if (password.contains(RegExp(r'[A-Z]'))) score++;
    if (password.contains(RegExp(r'[a-z]'))) score++;
    if (password.contains(RegExp(r'[0-9]'))) score++;
    if (password.contains(RegExp(r'[!@#\$%^&*(),.?":{}|<>]'))) score++;

    // Determine strength based on score
    String strength;
    if (score < 4) {
      strength = 'Weak';
    } else if (score < 6) {
      strength = 'Medium';
    } else {
      strength = 'Strong';
    }

    passwordStrength = strength;
    return passwordStrength;
    
  }

  /// Generates a strong random password

static  String generateStrongPassword({int length = 16}) {
    final random = Random.secure();
    const String upperCaseChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const String lowerCaseChars = 'abcdefghijklmnopqrstuvwxyz';
    const String digitChars = '0123456789';
    const String specialChars = '!@#\$%^&*()-_=+[]{}|;:,.<>?/';

    // Ensure at least one character from each category
    String password = '';
    password += upperCaseChars[random.nextInt(upperCaseChars.length)];
    password += lowerCaseChars[random.nextInt(lowerCaseChars.length)];
    password += digitChars[random.nextInt(digitChars.length)];
    password += specialChars[random.nextInt(specialChars.length)];

    // Fill the rest with random characters from all categories
    const String allChars =
        upperCaseChars + lowerCaseChars + digitChars + specialChars;
    for (int i = 4; i < length; i++) {
      password += allChars[random.nextInt(allChars.length)];
    }

    // Shuffle the password to avoid predictable patterns
    final List<String> passwordChars = password.split('');
    passwordChars.shuffle(random);

    return passwordChars.join('');
  }
}
