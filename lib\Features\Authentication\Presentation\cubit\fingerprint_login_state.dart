part of 'fingerprint_login_cubit.dart';

@immutable
class FingerprintLoginState {
  final bool isLoading;
  final bool isBiometricAvailable;
  final bool isAuthenticated;
  final List<BiometricType> availableBiometrics;
  final String? errorMessage;
  final String? successMessage;
  final Map<String, dynamic>? userData;

  const FingerprintLoginState({
    this.isLoading = false,
    this.isBiometricAvailable = false,
    this.isAuthenticated = false,
    this.availableBiometrics = const [],
    this.errorMessage,
    this.successMessage,
    this.userData,
  });

  FingerprintLoginState copyWith({
    bool? isLoading,
    bool? isBiometricAvailable,
    bool? isAuthenticated,
    List<BiometricType>? availableBiometrics,
    String? errorMessage,
    String? successMessage,
    Map<String, dynamic>? userData,
  }) {
    return FingerprintLoginState(
      isLoading: isLoading ?? this.isLoading,
      isBiometricAvailable: isBiometricAvailable ?? this.isBiometricAvailable,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      availableBiometrics: availableBiometrics ?? this.availableBiometrics,
      errorMessage: errorMessage,
      successMessage: successMessage,
      userData: userData,
    );
  }

  @override
  String toString() {
    return 'FingerprintLoginState('
        'isLoading: $isLoading, '
        'isBiometricAvailable: $isBiometricAvailable, '
        'isAuthenticated: $isAuthenticated, '
        'availableBiometrics: $availableBiometrics, '
        'errorMessage: $errorMessage, '
        'successMessage: $successMessage, '
        'userData: $userData'
        ')';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is FingerprintLoginState &&
        other.isLoading == isLoading &&
        other.isBiometricAvailable == isBiometricAvailable &&
        other.isAuthenticated == isAuthenticated &&
        other.availableBiometrics == availableBiometrics &&
        other.errorMessage == errorMessage &&
        other.successMessage == successMessage &&
        other.userData == userData;
  }

  @override
  int get hashCode {
    return isLoading.hashCode ^
        isBiometricAvailable.hashCode ^
        isAuthenticated.hashCode ^
        availableBiometrics.hashCode ^
        errorMessage.hashCode ^
        successMessage.hashCode ^
        userData.hashCode;
  }
}
