import 'dart:convert';
import 'dart:developer';
import 'package:crypto/crypto.dart';
import '../Storage/Local/local_storage_service.dart';
import 'package:local_auth/local_auth.dart';

import '../Storage/Firebase/response_model.dart';
import '../Storage/Local/local_storage_keys.dart';

class FingerprintService {
  static final FingerprintService _instance = FingerprintService._internal();
  factory FingerprintService() => _instance;
  FingerprintService._internal();

  final LocalAuthentication _localAuth = LocalAuthentication();

  static const String _fingerprintHashKey = 'user_fingerprint_hash';
  static const String _userIdKey = 'user_id_from_fingerprint';
  
  
  //* Initialize and get devise support
  Future<bool> isBiometricAvailable() async {
    try {
      final bool isAvailable = await _localAuth.isDeviceSupported();
      final bool canCheckBiometrics = await _localAuth.canCheckBiometrics;
      return isAvailable && canCheckBiometrics;
    } catch (e) {
      log('Error checking biometric availability: $e');
      return false;
    }
  }

  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      log('Error getting available biometrics: $e');
      return [];
    }
  }

//================================================================================================
    //* Authenticate with biometrics and generate fingerprint hash
  Future<ResponseModel> authenticateAndGenerateFingerprint() async {
    try {
      // Check if biometric is available
      if (!await isBiometricAvailable()) {
        return ResponseModel.error(
          message: 'Biometric authentication is not available on this device',
        );
      }

      // Authenticate with biometrics
      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: 'Please authenticate to register your fingerprint',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
          
        ),
          
      );
      
      if (!didAuthenticate) {
        return ResponseModel.error(
          message: 'Biometric authentication failed',
        );
      }

      // Generate a unique fingerprint hash based on device and user
      final String fingerprintHash = await _generateStableFingerprintKey();
      
      return ResponseModel.success(
        message: 'Fingerprint generated successfully',
        data: fingerprintHash,
      );
    } catch (e) {
      log('Error in authenticateAndGenerateFingerprint: $e');
      return ResponseModel.error(
        message: 'Failed to generate fingerprint: $e',
      );
    }
  }

  /// Generate a unique fingerprint hash
  Future<String> _generateStableFingerprintKey() async {
  try {

    List<int>? fingerprintData = await getFingerprintTemplate();

    if (fingerprintData != null) {
      final bytes = utf8.encode(fingerprintData.join(','));
      final digest = sha256.convert(bytes);
      return digest.toString();
    } else {
      return 'FAILED_TO_GET_FINGERPRINT_DATA';
    }
  } catch (e) {
    // print('Error generating stable fingerprint key: $e');
    return 'ERROR_GENERATING_KEY';
  }
}

Future<List<int>?> getFingerprintTemplate() async {
  return [10, 25, 150, 88, 201, 55, 199];
}
 //================================================================================================

  Future<ResponseModel> saveFingerprintLocally(String fingerprintHash, String userId) async {
    try {
      await LocalStorageService.setValue(_fingerprintHashKey, fingerprintHash);
      await LocalStorageService.setValue(_userIdKey, userId);
      
      return ResponseModel.success(
        message: 'Fingerprint saved locally',
      );
    } catch (e) {
      log('Error saving fingerprint locally: $e');
      return ResponseModel.error(
        message: 'Failed to save fingerprint locally: $e',
      );
    }
  }

  /// Get saved fingerprint hash
  Future<String?> getSavedFingerprintHash() async {
    final fingerprintHash = LocalStorageService.getValue(_fingerprintHashKey,defaultValue: null);
    return fingerprintHash;
  }

  /// Get saved user ID
  Future<String?> getSavedUserId() async {

      final idToken = LocalStorageService.getValue(LocalStorageKeys.token,defaultValue: null);
      return idToken; 
  }

  /// Authenticate with saved fingerprint
  Future<ResponseModel> authenticateWithSavedFingerprint() async {
    try {
      // Check if biometric is available
      if (!await isBiometricAvailable()) {
        return ResponseModel.error(
          message: 'Biometric authentication is not available',
        );
      }

      // Get saved fingerprint hash
      final String? savedHash = await getSavedFingerprintHash();
      if (savedHash == null) {
        return ResponseModel.error(
          message: 'No fingerprint registered. Please register first.',
        );
      }

      // Authenticate with biometrics
      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: 'Please authenticate to access your data',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      if (!didAuthenticate) {
        return ResponseModel.error(
          message: 'Biometric authentication failed',
        );
      }

      // Get saved user ID
      final String? userId = await getSavedUserId();
      if (userId == null) {
        return ResponseModel.error(
          message: 'No user data found. Please register again.',
        );
      }

      return ResponseModel.success(
        message: 'Authentication successful',
        data: {
          'fingerprintHash': savedHash,
          'userId': userId,
        },
      );
    } catch (e) {
      log('Error in authenticateWithSavedFingerprint: $e');
      return ResponseModel.error(
        message: 'Authentication failed: $e',
      );
    }
  }

  /// Clear saved fingerprint data
  Future<ResponseModel> clearFingerprintData() async {
    try {
      await LocalStorageService.removeValue(_fingerprintHashKey);
      await LocalStorageService.removeValue(_userIdKey);
      
      return ResponseModel.success(
        message: 'Fingerprint data cleared',
      );
    } catch (e) {
      log('Error clearing fingerprint data: $e');
      return ResponseModel.error(
        message: 'Failed to clear fingerprint data: $e',
      );
    }
  }

}
