import 'dart:convert';
import 'dart:developer';
import 'package:crypto/crypto.dart';
import 'package:dio/dio.dart';
import 'package:local_auth/local_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../Storage/Firebase/response_model.dart';

class FingerprintService {
  static final FingerprintService _instance = FingerprintService._internal();
  factory FingerprintService() => _instance;
  FingerprintService._internal();

  final LocalAuthentication _localAuth = LocalAuthentication();
  final Dio _dio = Dio();

  // Keys for SharedPreferences
  static const String _fingerprintHashKey = 'user_fingerprint_hash';
  static const String _userIdKey = 'user_id_from_fingerprint';

  /// Check if biometric authentication is available
  Future<bool> isBiometricAvailable() async {
    try {
      final bool isAvailable = await _localAuth.isDeviceSupported();
      final bool canCheckBiometrics = await _localAuth.canCheckBiometrics;
      return isAvailable && canCheckBiometrics;
    } catch (e) {
      log('Error checking biometric availability: $e');
      return false;
    }
  }

  /// Get available biometric types
  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      log('Error getting available biometrics: $e');
      return [];
    }
  }

  /// Authenticate with biometrics and generate fingerprint hash
  Future<ResponseModel> authenticateAndGenerateFingerprint() async {
    try {
      // Check if biometric is available
      if (!await isBiometricAvailable()) {
        return ResponseModel.error(
          message: 'Biometric authentication is not available on this device',
        );
      }

      // Authenticate with biometrics
      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: 'Please authenticate to register your fingerprint',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      if (!didAuthenticate) {
        return ResponseModel.error(
          message: 'Biometric authentication failed',
        );
      }

      // Generate a unique fingerprint hash based on device and user
      final String fingerprintHash = await _generateFingerprintHash();
      
      return ResponseModel.success(
        message: 'Fingerprint generated successfully',
        data: fingerprintHash,
      );
    } catch (e) {
      log('Error in authenticateAndGenerateFingerprint: $e');
      return ResponseModel.error(
        message: 'Failed to generate fingerprint: $e',
      );
    }
  }

  /// Generate a unique fingerprint hash
  Future<String> _generateFingerprintHash() async {
    try {
      // Get device info and current timestamp for uniqueness
      final String deviceInfo = await _getDeviceInfo();
      final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final String randomComponent = DateTime.now().microsecondsSinceEpoch.toString();
      
      // Combine all components
      final String combined = '$deviceInfo-$timestamp-$randomComponent';
      
      // Generate SHA-256 hash
      final bytes = utf8.encode(combined);
      final digest = sha256.convert(bytes);
      
      return digest.toString();
    } catch (e) {
      log('Error generating fingerprint hash: $e');
      // Fallback to timestamp-based hash
      final String fallback = DateTime.now().millisecondsSinceEpoch.toString();
      final bytes = utf8.encode(fallback);
      final digest = sha256.convert(bytes);
      return digest.toString();
    }
  }

  /// Get basic device info for fingerprint generation
  Future<String> _getDeviceInfo() async {
    // This is a simplified version. In a real app, you might want to use
    // device_info_plus package for more detailed device information
    return 'flutter-device-${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Save fingerprint hash locally
  Future<ResponseModel> saveFingerprintLocally(String fingerprintHash, String userId) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString(_fingerprintHashKey, fingerprintHash);
      await prefs.setString(_userIdKey, userId);
      
      return ResponseModel.success(
        message: 'Fingerprint saved locally',
      );
    } catch (e) {
      log('Error saving fingerprint locally: $e');
      return ResponseModel.error(
        message: 'Failed to save fingerprint locally: $e',
      );
    }
  }

  /// Get saved fingerprint hash
  Future<String?> getSavedFingerprintHash() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      return prefs.getString(_fingerprintHashKey);
    } catch (e) {
      log('Error getting saved fingerprint: $e');
      return null;
    }
  }

  /// Get saved user ID
  Future<String?> getSavedUserId() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      return prefs.getString(_userIdKey);
    } catch (e) {
      log('Error getting saved user ID: $e');
      return null;
    }
  }

  /// Authenticate with saved fingerprint
  Future<ResponseModel> authenticateWithSavedFingerprint() async {
    try {
      // Check if biometric is available
      if (!await isBiometricAvailable()) {
        return ResponseModel.error(
          message: 'Biometric authentication is not available',
        );
      }

      // Get saved fingerprint hash
      final String? savedHash = await getSavedFingerprintHash();
      if (savedHash == null) {
        return ResponseModel.error(
          message: 'No fingerprint registered. Please register first.',
        );
      }

      // Authenticate with biometrics
      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: 'Please authenticate to access your data',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      if (!didAuthenticate) {
        return ResponseModel.error(
          message: 'Biometric authentication failed',
        );
      }

      // Get saved user ID
      final String? userId = await getSavedUserId();
      if (userId == null) {
        return ResponseModel.error(
          message: 'No user data found. Please register again.',
        );
      }

      return ResponseModel.success(
        message: 'Authentication successful',
        data: {
          'fingerprintHash': savedHash,
          'userId': userId,
        },
      );
    } catch (e) {
      log('Error in authenticateWithSavedFingerprint: $e');
      return ResponseModel.error(
        message: 'Authentication failed: $e',
      );
    }
  }

  /// Clear saved fingerprint data
  Future<ResponseModel> clearFingerprintData() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.remove(_fingerprintHashKey);
      await prefs.remove(_userIdKey);
      
      return ResponseModel.success(
        message: 'Fingerprint data cleared',
      );
    } catch (e) {
      log('Error clearing fingerprint data: $e');
      return ResponseModel.error(
        message: 'Failed to clear fingerprint data: $e',
      );
    }
  }

  /// Register fingerprint with MXFace API (if you want to use external API)
  Future<ResponseModel> registerFingerprintWithAPI({
    required String fingerprintHash,
    required String userId,
    required String apiUrl,
    required String groupName,
  }) async {
    try {
      final response = await _dio.post(
        apiUrl,
        data: {
          'fingerprint': fingerprintHash,
          'external_id': userId,
          'group_name': groupName,
        },
        options: Options(
          headers: {
            'Content-Type': 'application/json; charset=UTF-8',
            // Add your API key here if needed
            // 'Authorization': 'Bearer YOUR_API_KEY',
          },
        ),
      );

      if (response.statusCode == 200) {
        return ResponseModel.success(
          message: 'Fingerprint registered with API successfully',
          data: response.data,
        );
      } else {
        return ResponseModel.error(
          message: 'Failed to register fingerprint with API: ${response.statusCode}',
        );
      }
    } on DioException catch (error) {
      log('DioException in registerFingerprintWithAPI: $error');
      return ResponseModel.error(
        message: 'Network error during fingerprint registration: ${error.message}',
      );
    } catch (e) {
      log('Error in registerFingerprintWithAPI: $e');
      return ResponseModel.error(
        message: 'Failed to register fingerprint with API: $e',
      );
    }
  }

  /// Search fingerprint with MXFace API (if you want to use external API)
  Future<ResponseModel> searchFingerprintWithAPI({
    required String fingerprintHash,
    required String apiUrl,
    required String groupName,
  }) async {
    try {
      final response = await _dio.post(
        apiUrl,
        data: {
          'fingerprint': fingerprintHash,
          'group_name': groupName,
        },
        options: Options(
          headers: {
            'Content-Type': 'application/json; charset=UTF-8',
            // Add your API key here if needed
            // 'Authorization': 'Bearer YOUR_API_KEY',
          },
        ),
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = response.data as Map<String, dynamic>;
        if (data['results'] != null && (data['results'] as List).isNotEmpty) {
          final String matchedId = (data['results'] as List)[0]['external_id'];
          return ResponseModel.success(
            message: 'Fingerprint found',
            data: matchedId,
          );
        } else {
          return ResponseModel.error(
            message: 'Fingerprint not recognized',
          );
        }
      } else {
        return ResponseModel.error(
          message: 'Search failed with status: ${response.statusCode}',
        );
      }
    } on DioException catch (error) {
      log('DioException in searchFingerprintWithAPI: $error');
      return ResponseModel.error(
        message: 'Network error during fingerprint search: ${error.message}',
      );
    } catch (e) {
      log('Error in searchFingerprintWithAPI: $e');
      return ResponseModel.error(
        message: 'Failed to search fingerprint: $e',
      );
    }
  }
}
