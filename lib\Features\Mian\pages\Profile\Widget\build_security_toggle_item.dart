import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/Resources/app_colors.dart';

import '../../../../../Core/Utils/Components/build_text.dart';

class BuildSecurityToggleItem extends StatelessWidget {
  final ThemeData theme;
  final IconData icon;
  final String title;
  final String subtitle;
  final bool isEnabled;
  final bool isAvailable;
  final Function(bool) onChanged;
  const BuildSecurityToggleItem({
    super.key,
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.isEnabled,
    required this.isAvailable,
    required this.onChanged,
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity: isAvailable ? 1.0 : 0.5,
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 12.h),
        child: Row(
          children: [
            Icon(icon, size: 20, color: AppColors.grey),
            16.horizontalSpace,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  buildText(text: title, style: theme.textTheme.titleMedium!),
                  buildText(text: subtitle, style: theme.textTheme.titleSmall!),

                  if (!isAvailable)
                    Text(
                      'Not available on this device',
                      style: theme.textTheme.labelMedium?.copyWith(
                        color: AppColors.error,
                      ),
                    ),
                ],
              ),
            ),
            Switch(
              value: isEnabled && isAvailable,
              onChanged: isAvailable ? onChanged : null,
              activeColor: AppColors.primary,
            ),
          ],
        ),
      ),
    );
  }
}
