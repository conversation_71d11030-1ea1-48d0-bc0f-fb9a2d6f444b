import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:icons_plus/icons_plus.dart';
import 'app_colors.dart';

class AppIcons {
  static const Icon passwordVisibility = Icon(EvaIcons.eye_outline);
  static const Icon passwordVisibilityOff = Icon(EvaIcons.eye_off_outline);

  static const Icon home = Icon(Bootstrap.house);
  static const Icon homeActive = Icon(Bootstrap.house_fill);
  static const Icon profille = Icon(Bootstrap.person);
  static const Icon profilleActive = Icon(Bootstrap.person_fill);
  static  Icon checkInternet =  Icon(Bootstrap.wifi_off,size: 0.25.sw,) ;
  static Icon accountVerification = Icon(
    Bootstrap.shield_exclamation,
    color: AppColors.orange,
    size: 20.w,
  );
   static Icon camera = Icon(
                  Bootstrap.camera,
                  size: 18,
                  color: AppColors.primary,
                );

  static Icon email =  Icon(Bootstrap.envelope, size: 20, color: Colors.grey[600]) ;
  static Icon phone =   Icon(Bootstrap.phone, size: 20, color: Colors.grey[600]) ;
}
