import 'package:flutter/material.dart';

import '../../Core/Resources/app_colors.dart';
import '../../Core/Resources/fonts.dart';

ThemeData themeLightData() => ThemeData(
  brightness: Brightness.light,
  primaryColor: AppColors.primary,
  textTheme: TextTheme(
    // Headlines
    headlineLarge: AppTextStyles.h3Bold(),
    headlineMedium: AppTextStyles.h4Bold(),
    headlineSmall: AppTextStyles.h6Bold(),

    // Body
    bodyLarge: AppTextStyles.bodyLargeMedium(),
    bodyMedium: AppTextStyles.bodyMediumMedium(),
    bodySmall: AppTextStyles.bodySmallSemiBold(),

    // Labels
    labelLarge: AppTextStyles.labelLarge(),
    labelMedium: AppTextStyles.labelMedium(),
    labelSmall: AppTextStyles.labelSmall(),

    // Display
    displayLarge: AppTextStyles.displayLarge(),
    displayMedium: AppTextStyles.displayMedium(),
    displaySmall: AppTextStyles.displaySmall(),

    // Title
    titleLarge: AppTextStyles.titleLarge(),
    titleMedium: AppTextStyles.titleMedium(),
    titleSmall: AppTextStyles.titleSmall(color: Colors.grey[600]),
  ),
);
