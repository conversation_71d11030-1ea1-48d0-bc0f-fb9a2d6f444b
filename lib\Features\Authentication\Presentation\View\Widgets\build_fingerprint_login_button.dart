import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:icons_plus/icons_plus.dart';
import '../../../../../Core/Utils/Components/show_message.dart';
import '../../cubit/fingerprint_login_cubit.dart';
import '../../../../Mian/pages/main_page.dart';

class BuildFingerprintLoginButton extends StatelessWidget {
  const BuildFingerprintLoginButton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => FingerprintLoginCubit(),
      child: BlocConsumer<FingerprintLoginCubit, FingerprintLoginState>(
        listener: (context, fingerprintState) {
          if (fingerprintState.isLoading) {
            showMessageScafold(
              context,
              message: 'Authenticating...',
              type: MessageType.loading,
            );
          } else {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          }
    
          if (fingerprintState.errorMessage != null) {
            showMessageScafold(
              context,
              message: fingerprintState.errorMessage!,
              type: MessageType.error,
            );
          }
    
          if (fingerprintState.isAuthenticated) {
            showMessageScafold(
              context,
              message: 'Login successful!',
              type: MessageType.success,
            );
            // Navigate to main page
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => const MainPage()),
            );
          }
        },
        builder: (context, fingerprintState) {
          return OutlinedButton.icon(
            onPressed:
                fingerprintState.isBiometricAvailable &&
                        !fingerprintState.isLoading
                    ? () =>
                        context
                            .read<FingerprintLoginCubit>()
                            .loginWithFingerprint()
                    : null,
            icon: Icon(Bootstrap.fingerprint),
            label: Text(
              fingerprintState.isBiometricAvailable
                  ? 'Login with Fingerprint'
                  : 'Fingerprint not available',
            ),
            style: OutlinedButton.styleFrom(
              padding: EdgeInsets.symmetric(vertical: 16.h),
              side: BorderSide(
                color:
                    fingerprintState.isBiometricAvailable
                        ? Theme.of(context).primaryColor
                        : Colors.grey,
              ),
            ),
          );
        },
      ),
    );
  }
}
