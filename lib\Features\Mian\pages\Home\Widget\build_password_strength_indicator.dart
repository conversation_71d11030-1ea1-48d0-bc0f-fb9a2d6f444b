import 'package:flutter/material.dart';
import 'package:icons_plus/icons_plus.dart';
import '../../../cubit/main_cubit.dart';

class BuildPasswordStrengthIndicator extends StatelessWidget {
  const BuildPasswordStrengthIndicator({
    super.key, required this.state,
  });
  final MainState state;
  @override
  Widget build(BuildContext context) {
    return  state.passwordStrength != null? Padding(
      padding: const EdgeInsets.only(top: 8.0, left: 4.0),
      child: Row(
        children: [
          Icon(
            state.passwordStrength == 'Strong'
                ? Bootstrap.shield_check
                : state.passwordStrength == 'Medium'
                ? Bootstrap.shield_exclamation
                : Bootstrap.shield_x,
            size: 16,
            color:
                state.passwordStrength == 'Strong'
                    ? Colors.green
                    : state.passwordStrength == 'Medium'
                    ? Colors.orange
                    : Colors.red,
          ),
          const SizedBox(width: 6),
          Text(
            '${state.passwordStrength} password',
            style: TextStyle(
              fontSize: 12,
              color:
                  state.passwordStrength == 'Strong'
                      ? Colors.green
                      : state.passwordStrength == 'Medium'
                      ? Colors.orange
                      : Colors.red,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    ):SizedBox();
  }
}
