import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:icons_plus/icons_plus.dart';
import '../../../../../Core/Resources/app_colors.dart';
import '../../../../Mian/cubit/main_cubit.dart';
import '../../../Data/Model/items_model.dart';
import '../Widget/build_empty_state.dart';
import '../Widget/password_box.dart';
import '../../../../../main.dart';
import 'add_password_dialog.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MainCubit, MainState>(
      builder: (context, state) {
        return Scaffold(
          body:
              state.items!.isEmpty
                  ? BuildEmptyState()
                  : SafeArea(
                    child: ListView.builder(
                      padding: const EdgeInsets.only(top: 8, bottom: 80),
                      itemCount: state.items?.length,
                      itemBuilder: (context, index) {
                        final item = state.items?[index];
                        return PasswordBox(
                          icon: item!.icon,
                          title: item.title,
                          password: item.password,
                          onDelete:
                              () => _deletePassword(index, state, context),
                        );
                      },
                    ),
                  ),
          floatingActionButton: FloatingActionButton(
            onPressed: ()async {
            await showAddPasswordDialog(context);
            },
            backgroundColor: AppColors.secondary,
            child: const Icon(Bootstrap.plus_lg),
          ),
        );
      },
    );
  }

/// Shows the add password dialog and returns the result
Future<ItemsModel?> showAddPasswordDialog(BuildContext context) async {
  return showDialog<ItemsModel?>(
    context: context,
    builder: (context) => const AddPasswordDialog(),
  );
}


  void _deletePassword(int index, MainState state, context) {
    final items = state.items?[index];

    buildShowDiolog(
      context,

      onAction: () {
        // context.read<HomeCubit>().deletePassword(items!.idItem);
        kNavigationService.goBack();
      },
      title: 'Delete Password',
      content: 'Are you sure you want to delete "${items?.title}"?',
      titleButton: "Delete",
    );
  }

  Future<dynamic> buildShowDiolog(
    context, {
    VoidCallback? onAction,
    String title = '',
    String titleButton = '',
    String content = '',
  }) {
    return showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(title),
            content: Text(content),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(onPressed: onAction, child: Text(titleButton)),
            ],
          ),
    );
  }
}
