import 'package:flutter/material.dart';
import 'package:icons_plus/icons_plus.dart';
import 'package:uuid/uuid.dart';
class ItemsModel {
  final String title;
  final String password;
  final IconData icon;
  final String? idItem;
  ItemsModel( {
    required this.title,
    required this.password,
    required this.icon,
     this.idItem,
  });

  Map<String, dynamic> toJson() {
    final uuid = Uuid();
    return {
      'title_account': title,
      'password_account': password,
      'icon_name': getNameByIcon(icon),
      'id_item':idItem??  uuid.v1(),
    };
  }  
  


  factory ItemsModel.fromJson(Map<String, dynamic> json) {
    return ItemsModel(
      idItem: json['id_item'],
      title: json['title_account'],
      password: json['password_account'],
      icon: getIconByName(json['icon_name'])??Bootstrap.lock,
    );
  }
 static  final Map<String, IconData> _iconMap = {
    'lock': Bootstrap.lock,
    'globe': Bootstrap.globe,
    'envelope': Bootstrap.envelope,
    'credit_card': Bootstrap.credit_card,
    'bag': Bootstrap.bag,
    'briefcase': Bootstrap.briefcase,
    'phone': Bootstrap.phone,
    'controller': Bootstrap.controller,
    'mortarboard': Bootstrap.mortarboard,
    'house': Bootstrap.house,
    'person_circle': Bootstrap.person_circle,
    'cloud': Bootstrap.cloud,
    'bank': Bootstrap.bank,
    'key': Bootstrap.key,
    'shield_lock': Bootstrap.shield_lock,
    'wallet': Bootstrap.wallet,
    'facebook': Bootstrap.facebook,
    'twitter': Bootstrap.twitter,
    'instagram': Bootstrap.instagram,
    'google': Bootstrap.google,
    'linkedin': Bootstrap.linkedin,
    'github': Bootstrap.github,
  };

  // List of available icons (can be derived from the map values if preferred)
  static List<IconData> get availableIcons => _iconMap.values.toList();
  // List of available icons names (can be derived from the map keys if preferred)
  static List<String> get availableIconsNames => _iconMap.keys.toList();

  /// Returns an IconData by its string name.
  /// Returns null if the icon name is not found.
  static IconData? getIconByName(String name) {
    return _iconMap[name];
  }

  /// Returns the string name of an IconData.
  /// Returns null if the IconData is not found in the map.
   String? getNameByIcon(IconData icon) {
    for (final entry in _iconMap.entries) {
      if (entry.value == icon) {
        return entry.key;
      }
    }
    return null;
  }
}


