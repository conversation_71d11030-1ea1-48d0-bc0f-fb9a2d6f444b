import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:icons_plus/icons_plus.dart';
import '../../../../../Core/Utils/Components/TextField/app_text_form_field.dart';
import '../../../../../Core/Utils/Components/build_text.dart';
import '../../../../../Core/Utils/Validator/validate.dart';
import '../Widget/build_action_buttons.dart';
import '../Widget/build_box_show_icons.dart';
import '../Widget/build_password_strength_indicator.dart';
import '../Widget/header_dialog_add_password.dart';
import '../../../../../Core/Models/items_model.dart';
import '../../../cubit/main_cubit.dart';

import '../../../../../Core/Resources/generate_password.dart';
import '../../../../../Core/Utils/Components/TextField/password_text__form_field.dart';

class AddPasswordDialog extends StatefulWidget {
  const AddPasswordDialog({super.key, this.item});
  final ItemsModel? item;
  @override
  State<AddPasswordDialog> createState() => _AddPasswordDialogState();
}

class _AddPasswordDialogState extends State<AddPasswordDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  void initState() {
    super.initState();
    if (widget.item != null) {
      _titleController.text = widget.item!.title;
      _passwordController.text = widget.item!.password;
      context.read<MainCubit>().iconChange(widget.item!.icon);
    }
    _passwordController.addListener(() {
      context.read<MainCubit>().listenForPasswordChanges(
        _passwordController.text,
      );
    });
  }

  @override
  void dispose() {
    _titleController.dispose();
    _passwordController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _generateRandomPassword() {
    final password = GeneratePassword.generateStrongPassword();
    _passwordController.text = password;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BlocBuilder<MainCubit, MainState>(
      builder: (context, state) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 0,
          backgroundColor: Colors.transparent,

          child: Container(
            padding: const EdgeInsets.all(20),
            height: 450.h,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(20),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Form(
              key: _formKey,
              child: ListView(
                children: [
                  HeaderDialogAddPassword(theme: theme),
                  24.verticalSpace,
                  buildText(
                    text: 'Choose Icon',
                    style: theme.textTheme.titleSmall!,
                  ),
                  8.verticalSpace,
                  SizedBox(
                    height: 56.h,
                    child: ListView(
                      scrollDirection: Axis.horizontal,
                      children:
                          ItemsModel.availableIcons.map((icon) {
                            final isSelected = icon == state.selectedIcon;
                            return BuildBoxShowIcons(
                              isSelected: isSelected,
                              theme: theme,
                              icon: icon,
                            );
                          }).toList(),
                    ),
                  ),
                  16.verticalSpace,

                  AppTextFormField(
                    controller: _titleController,
                    hintText: 'e.g. Facebook, Gmail, Bank Account',
                    keyboardType: TextInputType.name,
                    labelText: 'Title',

                    prefixIcon: const Icon(Bootstrap.type),
                    validator: (value) => customValidate(value: value ?? ""),
                  ),
                  16.verticalSpace,

                  CustomPasswordTextFromField(
                    controller: _passwordController,
                    isLogin: true,
                    fieldId: "AddItem",
                    hintText: '••••••••',
                    isAddItem: true,
                    actionIcon: _generateRandomPassword,
                  ),
                  20.verticalSpace,

                  BuildPasswordStrengthIndicator(state: state),

                  BuildActionButtons(
                    formKey: _formKey,
                    titleController: _titleController,
                    passwordController: _passwordController,
                    state: state,
                    id: widget.item?.idItem,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
