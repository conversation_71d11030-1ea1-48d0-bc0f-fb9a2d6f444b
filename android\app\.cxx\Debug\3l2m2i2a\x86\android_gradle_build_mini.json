{"buildFiles": ["C:\\Flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Flutter\\Apps\\keyper\\android\\app\\.cxx\\Debug\\3l2m2i2a\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Flutter\\Apps\\keyper\\android\\app\\.cxx\\Debug\\3l2m2i2a\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}