import 'package:flutter/material.dart';
import 'package:icons_plus/icons_plus.dart';
import '../../../../../Core/Resources/app_colors.dart';

class BuildVerifiedFields extends StatelessWidget {
  final bool isVerified;
  final VoidCallback? onVerify;
  const BuildVerifiedFields({
    super.key, required this.isVerified, this.onVerify,
  });

  @override
  Widget build(BuildContext context) {
    if (isVerified) {
      return Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.green.shade200),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Bootstrap.check_circle,
                          size: 12,
                          color: Colors.green.shade700,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Verified',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.green.shade700,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  );
    }
    else {
      return ElevatedButton.icon(
      onPressed: onVerify,
      icon: const Icon(Bootstrap.check_circle, size: 16),
      label: const Text('Verify Now'),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.success,
        foregroundColor: Colors.white,

        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 8,
        ),
        textStyle: const TextStyle(fontSize: 12),
      ),
    );
    }
  }
}

