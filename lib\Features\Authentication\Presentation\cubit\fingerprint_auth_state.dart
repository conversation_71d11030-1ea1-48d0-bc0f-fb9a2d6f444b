part of 'fingerprint_auth_cubit.dart';

@immutable
class FingerprintAuthState {
  final bool isLoading;
  final bool isBiometricAvailable;
  final bool isRegistered;
  final bool isAuthenticated;
  final List<BiometricType> availableBiometrics;
  final String? errorMessage;
  final String? successMessage;

  const FingerprintAuthState({
    this.isLoading = false,
    this.isBiometricAvailable = false,
    this.isRegistered = false,
    this.isAuthenticated = false,
    this.availableBiometrics = const [],
    this.errorMessage,
    this.successMessage,
  });

  FingerprintAuthState copyWith({
    bool? isLoading,
    bool? isBiometricAvailable,
    bool? isRegistered,
    bool? isAuthenticated,
    List<BiometricType>? availableBiometrics,
    String? errorMessage,
    String? successMessage,
  }) {
    return FingerprintAuthState(
      isLoading: isLoading ?? this.isLoading,
      isBiometricAvailable: isBiometricAvailable ?? this.isBiometricAvailable,
      isRegistered: isRegistered ?? this.isRegistered,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      availableBiometrics: availableBiometrics ?? this.availableBiometrics,
      errorMessage: errorMessage,
      successMessage: successMessage,
    );
  }

  @override
  String toString() {
    return 'FingerprintAuthState('
        'isLoading: $isLoading, '
        'isBiometricAvailable: $isBiometricAvailable, '
        'isRegistered: $isRegistered, '
        'isAuthenticated: $isAuthenticated, '
        'availableBiometrics: $availableBiometrics, '
        'errorMessage: $errorMessage, '
        'successMessage: $successMessage'
        ')';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is FingerprintAuthState &&
        other.isLoading == isLoading &&
        other.isBiometricAvailable == isBiometricAvailable &&
        other.isRegistered == isRegistered &&
        other.isAuthenticated == isAuthenticated &&
        other.availableBiometrics == availableBiometrics &&
        other.errorMessage == errorMessage &&
        other.successMessage == successMessage;
  }

  @override
  int get hashCode {
    return isLoading.hashCode ^
        isBiometricAvailable.hashCode ^
        isRegistered.hashCode ^
        isAuthenticated.hashCode ^
        availableBiometrics.hashCode ^
        errorMessage.hashCode ^
        successMessage.hashCode;
  }
}
