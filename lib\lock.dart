import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FingerprintLoginScreenDio extends StatefulWidget {
  @override
  _FingerprintLoginScreenDioState createState() =>
      _FingerprintLoginScreenDioState();
}

class _FingerprintLoginScreenDioState
    extends State<FingerprintLoginScreenDio> {
  String _loginResult = '';
  String? _localFingerprint;
  final Dio _dio = Dio();

  @override
  void initState() {
    super.initState();
    _loadLocalFingerprint();
  }

  Future<void> _loadLocalFingerprint() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      _localFingerprint = prefs.getString('user_fingerprint');
    });
  }

  Future<void> loginWithFingerprint() async {
    if (_localFingerprint == null) {
      setState(() {
        _loginResult = 'No fingerprint saved locally.';
      });
      return;
    }

    final String apiUrl =
        'YOUR_MXFACE_SEARCH_ENDPOINT'; // **استبدل بعنوان نقطة نهاية البحث**
    final String groupName =
        'YOUR_GROUP_NAME'; // **استبدل باسم المجموعة الخاصة بك في MXFace**

    try {
      final response = await _dio.post(
        apiUrl,
        data: {
          'fingerprint': _localFingerprint,
          'group_name': groupName,
        },
        options: Options(
          headers: {
            'Content-Type': 'application/json; charset=UTF-8',
            // قد تحتاج إلى إضافة مفتاح API أو أي رؤوس مصادقة أخرى هنا
          },
        ),
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = response.data as Map<String, dynamic>;
        if (data['results'] != null && (data['results'] as List).isNotEmpty) {
          // تم العثور على تطابق - يمكنك استخراج الـ ID من نتائج البحث (إذا كان MXFace يرجعه)
          final String matchedId = (data['results'] as List)[0]['external_id'];
          setState(() {
            _loginResult = 'Login successful. User ID: $matchedId';
            // هنا يمكنك جلب بيانات المستخدم من قاعدة البيانات الخاصة بك باستخدام matchedId
          });
        } else {
          setState(() {
            _loginResult = 'Fingerprint not recognized.';
          });
        }
      } else {
        setState(() {
          _loginResult =
              'Login failed with status: ${response.statusCode}';
        });
      }
    } on DioException catch (error) {
      setState(() {
        _loginResult = 'Error during login: ${error.message}';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Login with Fingerprint (Dio)'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            ElevatedButton(
              onPressed: loginWithFingerprint,
              child: Text('Login with Fingerprint'),
            ),
            SizedBox(height: 16),
            Text(
              _loginResult,
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),
            Text(
              _localFingerprint == null
                  ? 'No local fingerprint saved.'
                  : 'Local Fingerprint: ${_localFingerprint!.substring(0, 20)}...',
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

