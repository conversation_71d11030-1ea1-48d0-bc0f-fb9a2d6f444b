import 'dart:io';
import 'package:image_picker/image_picker.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:icons_plus/icons_plus.dart';
import 'package:keyper/main.dart';
import '../../../Config/Cubit/settings_cubit.dart';
import '../../../Config/Routes/app_routes.dart';
import '../../../Core/Models/user_model.dart';
import '../../../Core/Storage/Firebase/collection_data.dart';
import '../../../Core/Storage/Firebase/keys_data.dart';
import '../../../Core/Storage/Local/local_storage_service.dart';

import '../../../Core/Resources/generate_password.dart';
import '../../../Core/Storage/Firebase/firebase_service.dart';
import '../../../Core/Storage/Local/local_storage_keys.dart';
import '../../../Core/Models/items_model.dart';
import '../pages/Home/Pages/home_page.dart';
import '../pages/Profile/Page/profile_screen.dart';

part 'main_state.dart';

class MainCubit extends Cubit<MainState> {
  MainCubit() : super(MainState());
  Future<void> init(isInternet, {bool isRefresh = false}) async {
    bool isUser = state.user == null;
    if (!isInternet) {
      return;
    } else if ((isInternet && isUser) || isRefresh) {
      var resonseDataUser = await getData();
      if (resonseDataUser.isEmailVerified == false) {
        final isEmailVerified = await checkEmailVerified();
        if (isEmailVerified) {
          await FirebaseServices.instance.updateData(
            AppCollectionData.users,
            resonseDataUser.uid!,
            {AppKeysData.isEmailVerified: isEmailVerified},
          );
          resonseDataUser = await getData();
        }
      }

      emit(
        state.copyWith(
          loadingDataUSer: false,
          user: resonseDataUser,
          items: resonseDataUser.items,
        ),
      );
    }
  }

  bool isInternet(context) {
    return context.read<SettingsCubit>().state.internet;
  }

  Future<void> changeIndex(int index) async {
    emit(state.copyWith(index: index));
  }

  Widget buildPage(int index) {
    switch (index) {
      case 0:
        return const HomeScreen();
      case 1:
        return const ProfileScreen();
      default:
        return Container();
    }
  }

  Future<UserModel> getData() async {
    emit(state.copyWith(loadingDataUSer: true));

    final userId = LocalStorageService.getValue(LocalStorageKeys.token);
    // print(userId.toString());
    final respose = await FirebaseServices.instance.getUserData(
      userId,
      AppCollectionData.users,
    );
    final user = UserModel.fromJson(respose.data);
    // print(user.toJson());
    return user;
  }

  Future<void> addItem(ItemsModel? result) async {
    emit(state.copyWith(loadingaddItemData: true));
    final respose = await FirebaseServices.instance.updateData(
      AppCollectionData.users,
      state.user!.uid!,
      {
        AppKeysData.items: FieldValue.arrayUnion([result!.toJson()]),
      },
    );
    if (respose.status) {
      final itemes = state.items;
      emit(
        state.copyWith(
          items: [...?itemes, result],
          loadingaddItemData: false,
          messagesAddItemData: respose.message,
        ),
      );
    }
  }

  Future<void> updateItem(ItemsModel? result) async {
    // emit(state.copyWith(loadingUpdateItemData: true));
    try {
      final userDocRef = FirebaseFirestore.instance
          .collection(AppCollectionData.users)
          .doc(state.user!.uid!);

      final snapshot = await userDocRef.get();
      if (snapshot.exists) {
        final userData = snapshot.data();
        final currentItems =
            (userData?[AppKeysData.items] as List<dynamic>?)
                ?.map((item) => ItemsModel.fromJson(item))
                .toList() ??
            [];

        final indexToUpdate = currentItems.indexWhere(
          (item) => item.idItem == result!.idItem,
        );

        if (indexToUpdate != -1) {
          currentItems[indexToUpdate] =
              result!; // Replace with the updated item

          // Convert the updated list back to a list of JSON objects
          final updatedItemsJson =
              currentItems.map((item) => item.toJson()).toList();

          final response = await FirebaseServices.instance.updateData(
            AppCollectionData.users,
            state.user!.uid!,
            {AppKeysData.items: updatedItemsJson},
          );

          if (response.status) {
            emit(
              state.copyWith(
                items: currentItems,
                // loadingUpdateItemData: false,
                // messagesUpdateItemData: response.message,
              ),
            );
          } else {
            emit(
              state.copyWith(
                // loadingUpdateItemData: false,
                // errorUpdateItemData: response.message,
              ),
            );
          }
        } else {
          emit(
            state.copyWith(
              // loadingUpdateItemData: false,
              // errorUpdateItemData: 'Item not found for update.',
            ),
          );
        }
      } else {
        emit(
          state.copyWith(
            // loadingUpdateItemData: false,
            // errorUpdateItemData: 'User data not found.',
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          //   loadingUpdateItemData: false,
          //   errorUpdateItemData: e.toString(),
        ),
      );
    }
  }

  void iconChange(IconData icon) {
    emit(state.copyWith(selectedIcon: icon));
  }

  void listenForPasswordChanges(String password) {
    final strength = GeneratePassword.evaluatePasswordStrength(password);
    emit(state.copyWith(passwordStrength: strength));
  }

  Future<void> deletePassword(ItemsModel? result) async {
    emit(state.copyWith(loadingRemoveItemData: true));
    final respose = await FirebaseServices.instance.updateData(
      AppCollectionData.users,
      state.user!.uid!,
      {
        AppKeysData.items: FieldValue.arrayRemove([result!.toJson()]),
      },
    );
    if (respose.status) {
      final itemes = state.items;
      itemes?.removeWhere((element) => element.idItem == result.idItem);
      emit(
        state.copyWith(
          items: itemes,
          loadingRemoveItemData: false,
          messagesRemoveItemData: respose.message,
        ),
      );
    }
  }

  Future<void> deleteAccount() async {}

  Future<void> sendVerifyEmail() async {
    emit(state.copyWith(loadingVerifyEmail: true));
    await FirebaseServices.instance.sendEmailVerification();
    emit(state.copyWith(loadingVerifyEmail: false));
  }

  Future<void> verifyphone() async {
    emit(state.copyWith(loadingVerifyPhone: true));
    if (state.user!.phoneNumber == null) {
      emit(state.copyWith(loadingVerifyPhone: false));
      return;
    }
    final resposen = await FirebaseServices.instance.sendPhoneVerification(
      state.user!.phoneNumber!,
    );
    if (resposen.status) {
      emit(state.copyWith(loadingVerifyPhone: false));
    }
  }

  Future<bool> checkEmailVerified() async {
    final resposen = await FirebaseServices.instance.isEmailVerified();
    return Future.value(resposen.data);
  }

 final ImagePicker _picker = ImagePicker();

  Future<File?> pickImage() async {
    final XFile? pickedFile = await _picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      return File(pickedFile.path);
    }
    return null;
  }

  Future<void> uploadImage() async {
    final File? image = await pickImage();
    if (image == null) {
      return;
    }
    final resposen = await FirebaseServices.instance.uploadImage(
      image,
      "Profile",
    );
    await FirebaseServices.instance.updateData(
      AppCollectionData.users,
      state.user!.uid!,
      {AppKeysData.image: resposen.data},
    );
    if (resposen.status) {
      // Assuming your state has a 'user' and 'copyWith' method
      emit(state.copyWith(user: state.user!.copyWith(image: resposen.data)));
    }
  }
  Future<void> logout() async {
    await LocalStorageService.setValue(LocalStorageKeys.isLoggedIn, false);
    await LocalStorageService.removeValue(LocalStorageKeys.setData);
    await LocalStorageService.removeValue(LocalStorageKeys.token);
    kNavigationService.clearAndNavigateTo(AppRoutes.login);
  }

}
