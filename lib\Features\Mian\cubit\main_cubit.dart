import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:icons_plus/icons_plus.dart';
import 'package:keyper/Core/Models/user_model.dart';
import 'package:keyper/Core/Storage/Firebase/collection_data.dart';
import 'package:keyper/Core/Storage/Local/local_storage_service.dart';

import '../../../Core/Resources/generate_password.dart';
import '../../../Core/Storage/Firebase/firebase_service.dart';
import '../../../Core/Storage/Local/local_storage_keys.dart';
import '../../Home/Data/Model/items_model.dart';
import '../../Home/Presentation/View/Pages/home_page.dart';
import '../../Profile/Presentation/View/Page/profile_screen.dart';

part 'main_state.dart';

class MainCubit extends Cubit<MainState> {
  MainCubit() : super(MainState());
  init() async {
    emit(state.copyWith(loadingDataUSer: true));
    final resonseDataUser = await getData();
    log(resonseDataUser.toJson().toString());
    emit(
      state.copyWith(
        loadingDataUSer: false,
        user: resonseDataUser,
        items: [
          ItemsModel(
            title: 'Google',
            password: '123456',
            icon: Bootstrap.google,
            idItem: '1',
          ),
          ItemsModel(
            title: 'Facebook',
            password: '123456',
            icon: Bootstrap.facebook,
            idItem: '2',
          ),
          ItemsModel(
            title: 'Twitter',
            password: '123456',
            icon: Bootstrap.twitter,
            idItem: '3',
          ),
        ],
      ),
    );
  }

  Future<void> changeIndex(int index) async {
    emit(state.copyWith(index: index));
  }

  Widget buildPage(int index) {
    switch (index) {
      case 0:
        return const HomeScreen();
      case 1:
        return const ProfileScreen();
      default:
        return Container();
    }
  }

  Future<UserModel> getData() async {
    final userId = LocalStorageService.getValue(LocalStorageKeys.token);

    final respose = await FirebaseServices.instance.getUserData(
      userId,
      AppCollectionData.users,
    );
    final user = UserModel.fromJson(respose.data);
    return user;
  }

  void addItem(ItemsModel? result) {}
  // void addPassword(ItemsModel password) {
  //   emit(state.copyWith(passwords: [...state.passwords, password]));
  // }
  void iconChange(IconData icon) {
    emit(state.copyWith(selectedIcon: icon));
  }

  void listenForPasswordChanges(String password) {
    // Evaluate password strength and update the strength indicator
    final strength = GeneratePassword.evaluatePasswordStrength(password);
    emit(state.copyWith(passwordStrength: strength));
  }

  void deletePassword(String id) {
    // emit(state.copyWith(passwords: [
    //   ...state.passwords.sublist(0, index),
    //   ...state.passwords.sublist(index + 1)
    // ]));
  }
}
