import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../Core/Utils/Components/show_message.dart';
import '../Widgets/form_create_accounts.dart';
import '../../../../../Core/Utils/Components/defualt_app_bar.dart';
import '../../../../../Core/Resources/strings.dart';
import '../../../../../Config/Routes/app_routes.dart';
import '../../../../../main.dart';
import '../../cubit/auth_cubit.dart';

class CreateAccountScreen extends StatelessWidget {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  CreateAccountScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AuthCubit, AuthState>(
      listener: (context, state) {
        if (state.statusLogin != null) {
          if (state.statusSingUp == true) {
            showMessageScafold(context, message: state.message);
            kNavigationService.navigateTo(AppRoutes.main);
          } else if (state.statusSingUp == false) {
            showMessageScafold(context, message: state.message);
          }
        }
      },
      builder: (context, state) {
        return Scaffold(
          appBar: DefualtAppBar(text: AppStrings.createAccount),
          body: Center(
            child: SafeArea(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(32.0),
                child: FormCreateAccounts(
                  formKey: _formKey,
                  nameController: _nameController,
                  emailController: _emailController,
                  phoneController: _phoneController,
                  passwordController: _passwordController,
                  state: state,
                ),
              ),
            ),
          ),
          resizeToAvoidBottomInset: true,
        );
      },
    );
  }
}
