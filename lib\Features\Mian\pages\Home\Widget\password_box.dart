import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/Utils/Components/password_actions.dart';

class PasswordBox extends StatefulWidget {
  final IconData icon;
  final String title;
  final String password;
  final VoidCallback? onDelete;
  final VoidCallback? onEdit;

  const PasswordBox({
    super.key,
    required this.icon,
    required this.title,
    required this.password,
    this.onDelete,
    this.onEdit,
  });

  @override
  State<PasswordBox> createState() => _PasswordBoxState();
}

class _PasswordBoxState extends State<PasswordBox> {
  bool _obscureText = true;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16.0),
      margin: const EdgeInsets.all( 10.0),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(75),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 48.w,
            height: 48.h,
            decoration: BoxDecoration(
              color: theme.primaryColor.withAlpha(20),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(widget.icon, color: theme.primaryColor, size: 24),
          ),

          16.horizontalSpace,

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  widget.title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                4.verticalSpace,
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        _obscureText
                            ? '•' * 8
                            : widget.password,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[700],
                          letterSpacing: _obscureText ? 1.5 : 0,
                        ),
                       
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
            
              PasswordActions.buildVisibilityToggle(
                obscureText: _obscureText,
                onToggle: (value) {
                  setState(() {
                    _obscureText = value;
                  });
                },
                visibleColor: theme.primaryColor,
                hiddenColor: Colors.grey[400],
              ),

              // Copy button
              PasswordActions.buildCopyButton(
                context: context,
                textToCopy: widget.password,
                message: 'Password copied',
                color: theme.primaryColor,
              ),

              // More options
              IconButton(
                icon: Icon(Icons.more_vert, color: Colors.grey[600], size: 20),
                splashRadius: 20,
                onPressed: () {
                  PasswordActions.showPasswordOptions(
                    context: context,
                    password: widget.password,
                    title: widget.title,
                    onEdit: widget.onEdit,
                    onDelete: widget.onDelete,
                    onShare: () {
                      // Share functionality
                    },
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
}
