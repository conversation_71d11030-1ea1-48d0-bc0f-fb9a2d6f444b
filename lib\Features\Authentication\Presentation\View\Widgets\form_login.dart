import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Config/Routes/app_routes.dart';
import '../../../../../Core/Utils/Components/TextField/app_text_form_field.dart';
import '../../../../../Core/Utils/Components/TextField/password_text__form_field.dart';
import '../../../../../Core/Utils/Components/build_button_app.dart';
import '../../../../../Core/Utils/Components/build_text.dart';
import '../../../../../Core/Resources/fonts.dart';
import '../../../../../Core/Resources/strings.dart';
import '../../../../../Core/Utils/Validator/validate.dart';
import '../../../Data/Models/login_model.dart';
import '../../cubit/auth_cubit.dart';
import '../../../../../main.dart';

class FormLogin extends StatelessWidget {
  const FormLogin({
    super.key,
    required GlobalKey<FormState> formKey,
    required TextEditingController emailController,
    required TextEditingController passwordController,
    required this.state,
  }) : _formKey = formKey,
       _emailController = emailController,
       _passwordController = passwordController;

  final GlobalKey<FormState> _formKey;
  final TextEditingController _emailController;
  final TextEditingController _passwordController;
  final AuthState state;

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        spacing: 40.h,
        children: [
          buildText(
            text: AppStrings.welcome,
            textAlign: TextAlign.center,
            style: AppTextStyles.bodyLargeMedium(),
          ),

          AppTextFormField(
            controller: _emailController,
            labelText: AppStrings.email,
            hintText: AppStrings.emailHint,
            keyboardType: TextInputType.emailAddress,
            validator: (value) => validateEmail(email: value ?? ""),
            textInputAction: TextInputAction.next,
          ),
          CustomPasswordTextFromField(
            controller: _passwordController,
            hintText: '••••••••',
            fieldId: "loginPassword",
            isLogin: true,
          ),
          BuildButtonApp(
            onPressed: () {
              if (_formKey.currentState!.validate()) {
                final account = LoginModel(
                  emial: _emailController.text,
                  password: _passwordController.text,
                );
                context.read<AuthCubit>().login(account);
              }
            },
            text: AppStrings.login,
          ),

          TextButton(
            onPressed: () {
              kNavigationService.navigateTo(AppRoutes.sing);
            },
            child: Text('New here? Create an account'),
          ),
        ],
      ),
    );
  }
}
