import 'package:flutter/material.dart';

import '../../Features/Authentication/Presentation/View/Pages/create_account_screen.dart';
import '../../Features/Authentication/Presentation/View/Pages/login_screen.dart';
import '../../Features/Mian/pages/Home/Pages/home_page.dart';
import '../../Features/Mian/pages/Profile/Page/profile_screen.dart';
import '../../Features/Mian/pages/main_page.dart' ;

class AppRoutes {
  static const String splash = '/';
  static const String login = '/login';
  static const String sing = '/sing';
  static const String home = '/home';
  static const String main = '/main';
  static const String add = '/add';
  static const String user = '/user';
  static const String searcnh = '/search';
}

class AppRouteBuilders {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case AppRoutes.login:
        return _defaultPageRoute(LogInScreen());
      case AppRoutes.sing:
        return _defaultPageRoute(CreateAccountScreen());
      case AppRoutes.main:
        return _defaultPageRoute(MainPage());
      case AppRoutes.home:
        return _defaultPageRoute(HomeScreen());
      case AppRoutes.user:
        return _defaultPageRoute(ProfileScreen());
      default:
        return _errorRoute('No route defined for "${settings.name}"');
    }
  }

  static MaterialPageRoute<dynamic> _defaultPageRoute(Widget screen) {
    return MaterialPageRoute(builder: (_) => screen);
  }

  static Route<dynamic> _errorRoute(String message) {
    return MaterialPageRoute(
      builder:
          (_) => Scaffold(
            body: Center(
              child: Text(message, style: const TextStyle(fontSize: 18)),
            ),
          ),
    );
  }
}
