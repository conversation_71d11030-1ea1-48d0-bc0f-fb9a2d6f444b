import 'package:flutter/material.dart';
import 'package:keyper/Core/Utils/Components/password_actions.dart';

class PasswordBox extends StatefulWidget {
  final IconData icon;
  final String title;
  final String password;
  final VoidCallback? onDelete;
  final VoidCallback? onEdit;

  const PasswordBox({
    super.key,
    required this.icon,
    required this.title,
    required this.password,
    this.onDelete,
    this.onEdit,
  });

  @override
  State<PasswordBox> createState() => _PasswordBoxState();
}

class _PasswordBoxState extends State<PasswordBox>
    with SingleTickerProviderStateMixin {
  bool _obscureText = true;
  bool _isHovering = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.98).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    _animationController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    _animationController.reverse();
  }

  void _onTapCancel() {
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return MouseRegion(
      onEnter: (_) => setState(() => _isHovering = true),
      onExit: (_) => setState(() => _isHovering = false),
      child: GestureDetector(
        onTapDown: _onTapDown,
        onTapUp: _onTapUp,
        onTapCancel: _onTapCancel,
        onTap: () {
          // Show password details or options
        },
        child: AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(scale: _scaleAnimation.value, child: child);
          },
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.0),
              boxShadow: [
                BoxShadow(
                  color:
                      _isHovering
                          ? theme.primaryColor.withAlpha(40)
                          : Colors.black.withAlpha(15),
                  spreadRadius: _isHovering ? 2 : 0,
                  blurRadius: _isHovering ? 20 : 10,
                  offset: Offset(0, _isHovering ? 5 : 3),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  // Left section with icon
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: theme.primaryColor.withAlpha(20),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      widget.icon,
                      color: theme.primaryColor,
                      size: 24,
                    ),
                  ),

                  const SizedBox(width: 16),

                  // Middle section with title and password
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          widget.title,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                _obscureText
                                    ? '•' * widget.password.length
                                    : widget.password,
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: Colors.grey[700],
                                  letterSpacing: _obscureText ? 1.5 : 0,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            if (_isHovering)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.green.shade50,
                                  borderRadius: BorderRadius.circular(4),
                                  border: Border.all(
                                    color: Colors.green.shade200,
                                  ),
                                ),
                                child: Text(
                                  'Strong',
                                  style: TextStyle(
                                    color: Colors.green.shade700,
                                    fontSize: 10,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Right section with actions
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Visibility toggle
                      PasswordActions.buildVisibilityToggle(
                        obscureText: _obscureText,
                        onToggle: (value) {
                          setState(() {
                            _obscureText = value;
                          });
                        },
                        visibleColor: theme.primaryColor,
                        hiddenColor: Colors.grey[400],
                      ),

                      // Copy button
                      PasswordActions.buildCopyButton(
                        context: context,
                        textToCopy: widget.password,
                        message: 'Password copied',
                        color: theme.primaryColor,
                      ),

                      // More options
                      IconButton(
                        icon: Icon(
                          Icons.more_vert,
                          color: Colors.grey[600],
                          size: 20,
                        ),
                        splashRadius: 20,
                        onPressed: () {
                          PasswordActions.showPasswordOptions(
                            context: context,
                            password: widget.password,
                            title: widget.title,
                            onEdit: widget.onEdit,
                            onDelete: widget.onDelete,
                            onShare: () {
                              // Share functionality
                            },
                          );
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
