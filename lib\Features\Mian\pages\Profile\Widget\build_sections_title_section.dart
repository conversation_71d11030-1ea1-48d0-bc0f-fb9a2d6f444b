import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/Utils/Components/build_text.dart';
import 'build_verified_fields.dart';

class BuildSectionsTitleSection extends StatelessWidget {
  const BuildSectionsTitleSection({
    super.key,
    required this.text,
    required this.isVerified, this.onVerify, required this.textValue, required this.icons, required this.theme,
  });

  final String text;
  final String textValue;
  final Icon icons;
  final bool isVerified;
  final VoidCallback? onVerify;
  final ThemeData theme;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:  EdgeInsets.symmetric(vertical: 8.h, horizontal: 16.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
         icons,
          16.horizontalSpace,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                buildText(text: text, style: theme.textTheme.titleSmall!),
                4.verticalSpace,
                buildText(text: textValue, style: theme.textTheme.titleMedium!),
              ],
            ),
          ),
            BuildVerifiedFields( isVerified: isVerified,onVerify: onVerify),
           
        ],
      ),
    );
  }
}
