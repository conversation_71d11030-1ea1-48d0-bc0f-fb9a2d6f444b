import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/Resources/fonts.dart';
import '../../../../../Core/Utils/Components/build_text.dart';

class DividerWithOR extends StatelessWidget {
  const DividerWithOR({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(child: Divider()),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: buildText(
            text: "OR",
            style: AppTextStyles.bodyLargeMedium(),
          ),
        ),
        Expanded(child: Divider()),
      ],
    );
  }
}
