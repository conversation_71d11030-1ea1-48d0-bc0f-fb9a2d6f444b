import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../../Core/Storage/Firebase/keys_data.dart';


class CreateAccountModel {
  final String name;
  final String email;
  final String phoneNumber;
  final String password;
  CreateAccountModel({
    required this.name,
    required this.email,
    required this.phoneNumber,
    required this.password,
  });
  Map<String, dynamic> to<PERSON><PERSON>(String uid) {
    return {
      AppKeysData.userId: uid,
      AppKeysData.name: name,
      AppKeysData.email: email,
      AppKeysData.phoneNumber: phoneNumber,
      // AppKeysData.password: password,
      AppKeysData.isEmailVerified: false,
      AppKeysData.isPhoneVerified: false,
      AppKeysData.createdAt: FieldValue.serverTimestamp(),
      AppKeysData.updatedAt: FieldValue.serverTimestamp(),
      AppKeysData.items: [],
    };
  }

  // factory CreateAccount.fromJson(Map<String, dynamic> json) {
  //   return CreateAccount(
  //     name: json['name'],
  //     email: json['email'],
  //     phoneNumber: json['phone_number'],
  //     password: json['password'],
  //   );
  // }
}
