import 'package:flutter/material.dart';
import 'package:icons_plus/icons_plus.dart';

class BuildSecurityItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final VoidCallback onTap;
  const BuildSecurityItem({
        required this.icon,
    required this.title,
    required this.onTap,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            Icon(icon, size: 20, color: Colors.grey[600]),
            const SizedBox(width: 16),
            Expanded(child: Text(title, style: const TextStyle(fontSize: 16))),
            Icon(Bootstrap.chevron_right, size: 16, color: Colors.grey[400]),
          ],
        ),
      ),
    );
  }
}