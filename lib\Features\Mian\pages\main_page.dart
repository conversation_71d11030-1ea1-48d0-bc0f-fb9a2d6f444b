import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:keyper/Features/Mian/cubit/main_cubit.dart';

import '../../../Core/Utils/Components/show_message.dart';

class MainPage extends StatefulWidget {
  const MainPage({super.key});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {

@override
  void initState() {
    super.initState();
    context.read<MainCubit>().init();  
  }
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<MainCubit, MainState>(
      listener: (context, state) {
        // log(state.loadingDataUSer.toString());
        // if (state.loadingDataUSer != null) {
        if (state.loadingDataUSer == true || state.loadingDataUSer == null) {
          log('Loading...');
          showMessageScafold(
            context,
            message: 'Loading...',
            type: MessageType.loading,
          );
        }
        if (state.loadingDataUSer == false) {
          ScaffoldMessenger.of(context).hideCurrentSnackBar();
        }
        // }
        // if (state.messagesAddItemData != null) {
        //   showMessageScafold(context, message: state.messagesAddItemData);
        // }
        // if (state.messagesRemoveItemData != null) {
        //   showMessageScafold(context, message: state.messagesRemoveItemData);
        // }
      },
      builder: (context, state) {
        return Scaffold(
          body: context.read<MainCubit>().buildPage(state.index),
          bottomNavigationBar: BottomNavigationBar(
            onTap: (value) => context.read<MainCubit>().changeIndex(value),
            currentIndex: state.index,

            items: [
              BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
              BottomNavigationBarItem(
                icon: Icon(Icons.person),
                label: 'Profile',
              ),
            ],
          ),
        );
      },
    );
  }
}
