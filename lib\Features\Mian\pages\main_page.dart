
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../Config/Cubit/settings_cubit.dart';
import '../../../Core/Resources/app_icons.dart';
import '../../../Core/Resources/fonts.dart';
import '../../../Core/Resources/strings.dart';
import '../../../Core/Utils/Components/build_text.dart';
import '../cubit/main_cubit.dart';

import '../../../Core/Utils/Components/show_message.dart';

class MainPage extends StatefulWidget {
  const MainPage({super.key});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<MainCubit>().init(
        context.read<SettingsCubit>().state.internet,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<SettingsCubit, SettingsState>(
      listener: (context, state) {
        if (!state.internet) {
                showMessageScafold(
                  context,
                  message: 'Check your internet connection...',
                  type: MessageType.loading,
                );
              }
              if (state.internet) {
                // ScaffoldMessenger.of(context).removeCurrentSnackBar();
              }
      },
      child: BlocConsumer<MainCubit, MainState>(
        listener: (context, state) {
          if (state.loadingDataUSer == true) {
            Future.delayed(const Duration(seconds: 2), () {});
            ScaffoldMessenger.of(context).removeCurrentSnackBar();
            Future.delayed(const Duration(seconds: 2), () {});
            showMessageScafold(
              context,
              message: 'Loading Get Data...',
              type: MessageType.loading,
            );
          }
         else if (state.loadingDataUSer == false) {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          }

        if (state.loadingVerifyEmail !=null) 
          {  if (state.loadingVerifyEmail == true) {
            showMessageScafold(
              context,
              message: 'Sending Email verification please wait....',
              type: MessageType.loading,
            );
          }
         else if (state.loadingVerifyEmail == false) {
            ScaffoldMessenger.of(context).removeCurrentSnackBar();
            Future.delayed(const Duration(seconds: 2), () {});
            showMessageScafold(context, message: 'Email verification sent');
          }}
          if (state.loadingVerifyPhone == true) {
            showMessageScafold(
              context,
              message: 'Sending Phone verification please wait....',);
          }
          else if (state.loadingVerifyPhone == false) {
            ScaffoldMessenger.of(context).removeCurrentSnackBar();
            Future.delayed(const Duration(seconds: 2), () {});
            showMessageScafold(context, message: 'Phone verification sent');
          }

        },
        builder: (context, state) {
          return RefreshIndicator(
            onRefresh: () {
              context.read<MainCubit>().init(
        context.read<SettingsCubit>().state.internet,
        isRefresh: true,
      );
              return Future.value();
            },
            triggerMode: RefreshIndicatorTriggerMode.onEdge,
            child: Scaffold(
              body:
                  (context.watch<SettingsCubit>().state.internet)
                      ? state.user != null ? context.read<MainCubit>().buildPage(state.index): LinearProgressIndicator()
                      :  Align(child:Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          AppIcons.checkInternet,
                          TextButton(onPressed: (){
                            context.read<MainCubit>().init(
                              context.read<SettingsCubit>().state.internet,
                            );
                          }, child: buildText(text: AppStrings.noInternet, style: AppTextStyles.bodyLargeMedium(), textAlign: TextAlign.center,))
                        ],
                      ),
            ),
              bottomNavigationBar:
                  (context.watch<SettingsCubit>().state.internet &&
                          state.user != null)
                      ? BottomNavigationBar(
                        onTap:
                            (value) =>
                                context.read<MainCubit>().changeIndex(value),
                        currentIndex: state.index,
                        elevation: 5,
                        landscapeLayout:
                            BottomNavigationBarLandscapeLayout.spread,
                        enableFeedback: true,
            
                        items: [
                          BottomNavigationBarItem(
                            icon: AppIcons.home,
                            label: AppStrings.home,
                            activeIcon: AppIcons.homeActive,
                          ),
                          BottomNavigationBarItem(
                            icon: AppIcons.profille,
                            activeIcon: AppIcons.profilleActive,
                            label: AppStrings.profile,
                          ),
                        ],
                      )
                      : null,
            ),
          );
        },
      ),
    );
  }
}
