import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:keyper/Features/Mian/cubit/main_cubit.dart';

class BuildBoxShowIcons extends StatelessWidget {
  const BuildBoxShowIcons({
    super.key,
    required this.isSelected,
    required this.theme,
    required this.icon,
  });

  final bool isSelected;
  final ThemeData theme;
  final IconData icon;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => context.read<MainCubit>().iconChange(icon),
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        width: 48.w,
        height: 48.h,
        decoration: BoxDecoration(
          color:
              isSelected
                  ? theme.primaryColor
                  : theme.primaryColor.withAlpha(20),
          borderRadius: BorderRadius.circular(12),
          border:
              isSelected
                  ? null
                  : Border.all(color: theme.primaryColor.withAlpha(50)),
        ),
        child: Icon(
          icon,
          color: isSelected ? Colors.white : theme.primaryColor,
          size: 24,
        ),
      ),
    );
  }
}
