import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:icons_plus/icons_plus.dart';
import '../../../../../Core/Models/user_model.dart';
import '../../../../../Core/Resources/app_icons.dart';
import '../../../../../Core/Resources/strings.dart';
import '../../../../../Core/Utils/Components/build_text.dart';
import '../../../cubit/main_cubit.dart';
import 'build_sections_title_section.dart';
import 'build_security_item.dart';
import 'build_security_toggle_item.dart';

class BuildProfileInformationSection extends StatefulWidget {
  const BuildProfileInformationSection({
    super.key,
    required this.user,
    required this.theme,
  });
  final UserModel user;
  final ThemeData theme;

  @override
  State<BuildProfileInformationSection> createState() =>
      _BuildProfileInformationSectionState();
}

class _BuildProfileInformationSectionState
    extends State<BuildProfileInformationSection> {
  String get name => widget.user.name ?? "";
  bool get isEmailVerified => widget.user.isEmailVerified ?? false;
  bool get isPhoneVerified => widget.user.isPhoneVerified ?? false;
  String get email => widget.user.email ?? "";
  String get phoneNumber => widget.user.phoneNumber ?? "";

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(20).w,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTitleSection(widget.theme, AppStrings.personalInformation),
          16.verticalSpace,
          BuildSectionsTitleSection(
            text: AppStrings.email,
            isVerified: isEmailVerified,
            icons: AppIcons.email,
            textValue: email,
            onVerify: _verifyEmail,
            theme: widget.theme,
          ),
          const Divider(),
          BuildSectionsTitleSection(
            text: AppStrings.phoneNumber,
            isVerified: isPhoneVerified,
            icons: AppIcons.phone,
            textValue: phoneNumber,
            onVerify: _verifyPhone,
            theme: widget.theme,
          ),

          Divider(thickness: 2.h),
          32.verticalSpace,

          _buildTitleSection(widget.theme, "Security"),
          16.verticalSpace,
          BuildSecurityItem(
            icon: Bootstrap.key,
            title: 'Change Password',
            onTap: () => _changePassword(),
          ),
          16.verticalSpace,

          BuildSecurityItem(
            icon: Bootstrap.filetype_key,
            title: 'Create Key encryption',
            onTap: () {},
          ),

          const Divider(),

          BuildSecurityToggleItem(
            theme: widget.theme,
            icon: Bootstrap.fingerprint,
            title: 'Biometric Authentication',
            subtitle: 'Use fingerprint or face ID to login',
            isEnabled: false,
            isAvailable: true,
            onChanged: (value) {},
          ),
          16.verticalSpace,

          const Divider(),
          16.verticalSpace,

           BuildSecurityItem(
            icon:   Bootstrap.box_arrow_right,
            title: AppStrings.logOut,
            onTap: () => context.read<MainCubit>().logout(),
          ),
        ],
      ),
    );
  }

  Widget _buildTitleSection(theme, String text) {
    return buildText(
      text: text,
      style: theme.textTheme.titleMedium!.copyWith(fontWeight: FontWeight.bold),
    );
  }

  void _verifyEmail() async {
    
     context.read<MainCubit>().sendVerifyEmail();
  }

  void _verifyPhone() {
  
    context.read<MainCubit>().verifyphone();
  }

  void _changePassword() {
    // Implement password change logic
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Change Password'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  obscureText: true,
                  decoration: InputDecoration(labelText: 'Current Password'),
                ),
                16.verticalSpace,
                TextField(
                  obscureText: true,
                  decoration: InputDecoration(labelText: 'New Password'),
                ),
                16.verticalSpace,
                TextField(
                  obscureText: true,
                  decoration: InputDecoration(
                    labelText: 'Confirm New Password',
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Change'),
              ),
            ],
          ),
    );
  }
}
