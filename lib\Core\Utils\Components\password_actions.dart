import 'package:flutter/material.dart';
import 'package:icons_plus/icons_plus.dart';
import '../../../main.dart';
import '../clipboard_utils.dart';

class PasswordActions {
  static Future<void> showPasswordOptions({
    required BuildContext context,
    required String password,
    required String title,
    VoidCallback? onEdit,
    VoidCallback? onDelete,
    VoidCallback? onShare,
  }) async {
    final theme = Theme.of(context);

    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder:
          (context) => SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            title,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                         
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Bootstrap.x_lg),
                          onPressed: () => kNavigationService.goBack(),
                          splashRadius: 20,
                        ),
                      ],
                    ),
                  ),

                  const Divider(),

                  // Copy option
                  // ListTile(
                  //   leading: Icon(
                  //     Bootstrap.clipboard,
                  //     color: theme.primaryColor,
                  //   ),
                  //   title: const Text('Copy password'),
                  //   onTap: () {
                  //     kNavigationService.goBack();
                  //     ClipboardUtils.copyToClipboard(
                  //       context: context,
                  //       text: password,
                  //       message: 'Password copied',
                  //     );
                  //   },
                  // ),

                  // Edit option
                  if (onEdit != null)
                    ListTile(
                      leading: Icon(
                        Bootstrap.pencil_square,
                        color: theme.primaryColor,
                      ),
                      title: const Text('Edit'),
                      onTap: () {
                      kNavigationService.goBack();
                        onEdit();
                      },
                    ),

                  // Share option
                  if (onShare != null)
                    ListTile(
                      leading: const Icon(Bootstrap.share, color: Colors.blue),
                      title: const Text('Share securely'),
                      onTap: () {
                       kNavigationService.goBack();
                        onShare();
                      },
                    ),

                  // Delete option
                  if (onDelete != null)
                    ListTile(
                      leading: const Icon(Bootstrap.trash, color: Colors.red),
                      title: const Text('Delete'),
                      onTap: () {
                       kNavigationService.goBack();
                        onDelete();
                      },
                    ),
                ],
              ),
            ),
          ),
    );
  }

  /// Creates a copy button for passwords
  static Widget buildCopyButton({
    required BuildContext context,
    required String textToCopy,
    String? message,
    double size = 20,
    double splashRadius = 20,
    Color? color,
  }) {
    final theme = Theme.of(context);

    return IconButton(
      icon: Icon(
        Bootstrap.clipboard,
        size: size,
        color: color ?? theme.primaryColor,
      ),
      splashRadius: splashRadius,
      onPressed: () {
        ClipboardUtils.copyToClipboard(
          context: context,
          text: textToCopy,
          message: message ?? 'Password copied',
        );
      },
    );
  }

  /// Creates a visibility toggle button for passwords
  static Widget buildVisibilityToggle({
    required bool obscureText,
    required Function(bool) onToggle,
    double size = 20,
    double splashRadius = 20,
    Color? visibleColor,
    Color? hiddenColor,
  }) {
    return IconButton(
      icon: Icon(
        obscureText ? Bootstrap.eye : Bootstrap.eye_slash,
        size: size,
        color:
            obscureText
                ? (hiddenColor ?? Colors.grey[400])
                : (visibleColor ?? Colors.blue),
      ),
      splashRadius: splashRadius,
      onPressed: () {
        onToggle(!obscureText);
      },
    );
  }
}
