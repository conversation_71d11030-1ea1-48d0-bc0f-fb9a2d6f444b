import 'package:bloc/bloc.dart';
import 'package:local_auth/local_auth.dart';
import 'package:meta/meta.dart';

import '../../../../Core/Services/fingerprint_service.dart';
import '../../../../Core/Storage/Firebase/firebase_service.dart';
import '../../../../Core/Storage/Local/local_storage_service.dart';
import '../../../../Core/Storage/Local/local_storage_keys.dart';

part 'fingerprint_auth_state.dart';

class FingerprintAuthCubit extends Cubit<FingerprintAuthState> {
  FingerprintAuthCubit() : super(FingerprintAuthState()) {
    _init();
  }

  final FingerprintService _fingerprintService = FingerprintService();
  final FirebaseServices _firebaseService = FirebaseServices.instance;

  Future<void> _init() async {
    await checkBiometricAvailability();
    await _checkIfAlreadyRegistered();
  }

  /// Check if biometric authentication is available
  Future<void> checkBiometricAvailability() async {
    emit(state.copyWith(isLoading: true));

    try {
      final bool isAvailable = await _fingerprintService.isBiometricAvailable();
      final List<BiometricType> availableBiometrics = await _fingerprintService.getAvailableBiometrics();

      emit(state.copyWith(
        isLoading: false,
        isBiometricAvailable: isAvailable,
        availableBiometrics: availableBiometrics,
        errorMessage: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        isBiometricAvailable: false,
        errorMessage: 'Failed to check biometric availability: $e',
      ));
    }
  }

  /// Check if user is already registered
  Future<void> _checkIfAlreadyRegistered() async {
    try {
      final String? savedHash = await _fingerprintService.getSavedFingerprintHash();
      final String? savedUserId = await _fingerprintService.getSavedUserId();
      
      emit(state.copyWith(
        isRegistered: savedHash != null && savedUserId != null,
      ));
    } catch (e) {
      // If there's an error checking, assume not registered
      emit(state.copyWith(isRegistered: false));
    }
  }

  /// Register user with fingerprint
  Future<void> registerWithFingerprint({
    required String name,
    String? email,
    String? phoneNumber,
  }) async {
    if (!state.isBiometricAvailable) {
      emit(state.copyWith(
        errorMessage: 'Biometric authentication is not available',
      ));
      return;
    }

    emit(state.copyWith(
      isLoading: true,
      errorMessage: null,
      successMessage: null,
    ));

    try {
      // Step 1: Authenticate and generate fingerprint hash
      final fingerprintResponse = await _fingerprintService.authenticateAndGenerateFingerprint();
      
      if (!fingerprintResponse.status) {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: fingerprintResponse.message,
        ));
        return;
      }

      final String fingerprintHash = fingerprintResponse.data as String;

      // Step 2: Check if fingerprint already exists
      final checkResponse = await _firebaseService.checkFingerprintExists(fingerprintHash);
      if (checkResponse.status) {
        final Map<String, dynamic> checkData = checkResponse.data as Map<String, dynamic>;
        if (checkData['exists'] == true) {
          emit(state.copyWith(
            isLoading: false,
            errorMessage: 'This fingerprint is already registered with another account',
          ));
          return;
        }
      }

      // Step 3: Register user with Firebase
      final registerResponse = await _firebaseService.registerUserWithFingerprint(
        name: name,
        fingerprintHash: fingerprintHash,
        email: email,
        phoneNumber: phoneNumber,
      );

      if (!registerResponse.status) {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: registerResponse.message,
        ));
        return;
      }

      final String userId = registerResponse.data as String;

      // Step 4: Save fingerprint data locally
      final saveResponse = await _fingerprintService.saveFingerprintLocally(
        fingerprintHash,
        userId,
      );

      if (!saveResponse.status) {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: 'Registration successful but failed to save locally: ${saveResponse.message}',
        ));
        return;
      }

      // Step 5: Save user ID to local storage
      await LocalStorageService.setValue(LocalStorageKeys.token, userId);

      emit(state.copyWith(
        isLoading: false,
        isRegistered: true,
        isAuthenticated: true,
        successMessage: 'Registration successful! Welcome, $name!',
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: 'Registration failed: $e',
      ));
    }
  }

  /// Login with fingerprint
  Future<void> loginWithFingerprint() async {
    if (!state.isBiometricAvailable) {
      emit(state.copyWith(
        errorMessage: 'Biometric authentication is not available',
      ));
      return;
    }

    emit(state.copyWith(
      isLoading: true,
      errorMessage: null,
      successMessage: null,
    ));

    try {
      // Step 1: Authenticate with saved fingerprint
      final authResponse = await _fingerprintService.authenticateWithSavedFingerprint();
      
      if (!authResponse.status) {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: authResponse.message,
        ));
        return;
      }

      final Map<String, dynamic> authData = authResponse.data as Map<String, dynamic>;
      final String fingerprintHash = authData['fingerprintHash'] as String;
      final String userId = authData['userId'] as String;

      // Step 2: Verify with Firebase
      final loginResponse = await _firebaseService.loginWithFingerprint(fingerprintHash);
      
      if (!loginResponse.status) {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: loginResponse.message,
        ));
        return;
      }

      // Step 3: Save user ID to local storage
      await LocalStorageService.setValue(LocalStorageKeys.token, userId);

      emit(state.copyWith(
        isLoading: false,
        isAuthenticated: true,
        successMessage: 'Login successful! Welcome back!',
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: 'Login failed: $e',
      ));
    }
  }

  /// Clear fingerprint data
  Future<void> clearFingerprintData() async {
    emit(state.copyWith(
      isLoading: true,
      errorMessage: null,
      successMessage: null,
    ));

    try {
      // Clear local fingerprint data
      final clearResponse = await _fingerprintService.clearFingerprintData();
      
      if (!clearResponse.status) {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: clearResponse.message,
        ));
        return;
      }

      // Clear local storage
      await LocalStorageService.clear();

      emit(state.copyWith(
        isLoading: false,
        isRegistered: false,
        isAuthenticated: false,
        successMessage: 'Fingerprint data cleared successfully',
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to clear fingerprint data: $e',
      ));
    }
  }

  /// Clear messages
  void clearMessages() {
    emit(state.copyWith(
      errorMessage: null,
      successMessage: null,
    ));
  }

  /// Reset authentication state
  void resetAuthState() {
    emit(state.copyWith(
      isAuthenticated: false,
      errorMessage: null,
      successMessage: null,
    ));
  }
}
