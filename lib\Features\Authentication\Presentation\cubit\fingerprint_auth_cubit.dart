import 'package:bloc/bloc.dart';
import 'package:local_auth/local_auth.dart';
import 'package:meta/meta.dart';

import '../../../../Core/Services/fingerprint_service.dart';
import '../../../../Core/Storage/Local/local_storage_service.dart';
import '../../../../Core/Storage/Local/local_storage_keys.dart';

part 'fingerprint_auth_state.dart';

class FingerprintAuthCubit extends Cubit<FingerprintAuthState> {
  FingerprintAuthCubit() : super(FingerprintAuthState()) {
    _init();
  }

  final FingerprintService _fingerprintService = FingerprintService();

  Future<void> _init() async {
    await checkBiometricAvailability();
    await _checkIfAlreadyRegistered();
  }

  /// Check if biometric authentication is available
  Future<void> checkBiometricAvailability() async {
    emit(state.copyWith(isLoading: true));

    try {
      final bool isAvailable = await _fingerprintService.isBiometricAvailable();
      final List<BiometricType> availableBiometrics =
          await _fingerprintService.getAvailableBiometrics();

      emit(
        state.copyWith(
          isLoading: false,
          isBiometricAvailable: isAvailable,
          availableBiometrics: availableBiometrics,
          errorMessage: null,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          isBiometricAvailable: false,
          errorMessage: 'Failed to check biometric availability: $e',
        ),
      );
    }
  }

  /// Check if user is already registered
  Future<void> _checkIfAlreadyRegistered() async {
    try {
      final String? savedHash =
          await _fingerprintService.getSavedFingerprintHash();
      final String? savedUserId = await LocalStorageService.getValue(
        LocalStorageKeys.token,
      );

      emit(
        state.copyWith(isRegistered: savedHash != null && savedUserId != null),
      );
    } catch (e) {
      // If there's an error checking, assume not registered
      emit(state.copyWith(isRegistered: false));
    }
  }

}
