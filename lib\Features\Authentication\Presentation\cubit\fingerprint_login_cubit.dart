import 'package:bloc/bloc.dart';
import 'package:local_auth/local_auth.dart';
import 'package:meta/meta.dart';

import '../../../../Core/Services/fingerprint_service.dart';
import '../../../../Core/Storage/Firebase/firebase_service.dart';
import '../../../../Core/Storage/Local/local_storage_service.dart';
import '../../../../Core/Storage/Local/local_storage_keys.dart';

part 'fingerprint_login_state.dart';

class FingerprintLoginCubit extends Cubit<FingerprintLoginState> {
  FingerprintLoginCubit() : super(FingerprintLoginState()) {
    _init();
  }

  final FingerprintService _fingerprintService = FingerprintService();
  final FirebaseServices _firebaseService = FirebaseServices.instance;

  Future<void> _init() async {
    await checkBiometricAvailability();
  }

  /// Check if biometric authentication is available
  Future<void> checkBiometricAvailability() async {
    emit(state.copyWith(isLoading: true));

    try {
      final bool isAvailable = await _fingerprintService.isBiometricAvailable();
      final List<BiometricType> availableBiometrics = await _fingerprintService.getAvailableBiometrics();

      emit(state.copyWith(
        isLoading: false,
        isBiometricAvailable: isAvailable,
        availableBiometrics: availableBiometrics,
        errorMessage: null,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        isBiometricAvailable: false,
        errorMessage: 'Failed to check biometric availability: $e',
      ));
    }
  }

  /// Login with fingerprint
  Future<void> loginWithFingerprint() async {
    if (!state.isBiometricAvailable) {
      emit(state.copyWith(
        errorMessage: 'Biometric authentication is not available',
      ));
      return;
    }

    emit(state.copyWith(
      isLoading: true,
      errorMessage: null,
      successMessage: null,
    ));

    try {
      // Step 1: Authenticate with biometrics and generate fingerprint hash
      final fingerprintResponse = await _fingerprintService.authenticateAndGenerateFingerprint();
      
      if (!fingerprintResponse.status) {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: fingerprintResponse.message,
        ));
        return;
      }

      final String fingerprintHash = fingerprintResponse.data as String;

      // Step 2: Search for fingerprint in lock collection and get user data
      final loginResponse = await _firebaseService.loginWithFingerprint(fingerprintHash);
      
      if (!loginResponse.status) {
        emit(state.copyWith(
          isLoading: false,
          errorMessage: loginResponse.message,
        ));
        return;
      }

      // Step 3: Extract user data from response
      final Map<String, dynamic> responseData = loginResponse.data as Map<String, dynamic>;
      final String userId = responseData['userId'] as String;

      // Step 4: Save user ID to local storage
      await LocalStorageService.setValue(LocalStorageKeys.token, userId);

      // Step 5: Save fingerprint data locally for future use
      await _fingerprintService.saveFingerprintLocally(fingerprintHash, userId);

      emit(state.copyWith(
        isLoading: false,
        isAuthenticated: true,
        successMessage: 'Login successful! Welcome back!',
        userData: responseData['userData'],
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: 'Login failed: $e',
      ));
    }
  }

  /// Clear messages
  void clearMessages() {
    emit(state.copyWith(
      errorMessage: null,
      successMessage: null,
    ));
  }

  /// Reset authentication state
  void resetAuthState() {
    emit(state.copyWith(
      isAuthenticated: false,
      errorMessage: null,
      successMessage: null,
      userData: null,
    ));
  }
}
