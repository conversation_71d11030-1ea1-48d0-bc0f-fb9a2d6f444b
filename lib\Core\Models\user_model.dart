import 'package:cloud_firestore/cloud_firestore.dart';
import 'items_model.dart';

import '../Storage/Firebase/keys_data.dart';

class UserModel {
  final String? name;
  final String? uid;
  final String? email;
  final String? phoneNumber;
  final String? password;
  final String? image;
  final String? fingerprintHash; // إضافة البصمة
  final bool? isEmailVerified;
  final bool? isPhoneVerified;
  final bool? isFingerprintRegistered; // إضافة حالة تسجيل البصمة
  final Timestamp? createdAt;
  final Timestamp? updatedAt;
  final List<ItemsModel>? items;
  UserModel({
    required this.uid,
    required this.image,
    required this.name,
    required this.email,
    required this.phoneNumber,
    required this.password,
    this.fingerprintHash, // إضافة البصمة
    required this.isEmailVerified,
    required this.isPhoneVerified,
    this.isFingerprintRegistered, // إضافة حالة تسجيل البصمة
    required this.createdAt,
    required this.updatedAt,
    required this.items,
  });
  Map<String, dynamic> toJson() {
    return {
      AppKeysData.userId: uid,
      AppKeysData.name: name,
      AppKeysData.email: email,
      AppKeysData.phoneNumber: phoneNumber,
      AppKeysData.image: image ?? "",
      AppKeysData.fingerprintHash: fingerprintHash,
      // AppKeysData.password: password,
      AppKeysData.isEmailVerified: isEmailVerified,
      AppKeysData.isPhoneVerified: isPhoneVerified,
      AppKeysData.isFingerprintRegistered: isFingerprintRegistered,
      AppKeysData.createdAt: createdAt,
      AppKeysData.updatedAt: FieldValue.serverTimestamp(),
      AppKeysData.items: items,
    };
  }

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      uid: json[AppKeysData.userId] as String?, // Allow null
      image: json[AppKeysData.image] as String?, // Allow null
      name: json[AppKeysData.name] as String?, // Allow null
      email: json[AppKeysData.email] as String?, // Allow null
      phoneNumber: json[AppKeysData.phoneNumber] as String?, // Allow null
      password: json[AppKeysData.password] as String?, // Allow null
      fingerprintHash:
          json[AppKeysData.fingerprintHash] as String?, // Allow null
      isEmailVerified: json[AppKeysData.isEmailVerified] as bool?, // Allow null
      isPhoneVerified: json[AppKeysData.isPhoneVerified] as bool?, // Allow null
      isFingerprintRegistered:
          json[AppKeysData.isFingerprintRegistered] as bool?, // Allow null
      createdAt: json[AppKeysData.createdAt] as Timestamp?, // Allow null
      updatedAt: json[AppKeysData.updatedAt] as Timestamp?, // Allow null
      items:
          (json[AppKeysData.items] as List?)
              ?.map((e) => ItemsModel.fromJson(e))
              .toList() ??
          [], // Null-aware map and default to empty list
    );
  }

  UserModel copyWith({
    String? name,
    String? email,
    String? phoneNumber,
    String? password,
    String? fingerprintHash,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    bool? isFingerprintRegistered,
    Timestamp? createdAt,
    Timestamp? updatedAt,
    List<ItemsModel>? items,
    String? image,
  }) {
    return UserModel(
      name: name ?? this.name,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      password: password ?? this.password,
      fingerprintHash: fingerprintHash ?? this.fingerprintHash,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      isFingerprintRegistered:
          isFingerprintRegistered ?? this.isFingerprintRegistered,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      items: items ?? this.items,
      uid: uid,
      image: image ?? this.image,
    );
  }
}
