import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/Resources/app_colors.dart';
import '../../../../../Core/Resources/app_icons.dart';
import '../../../../../Core/Resources/strings.dart';

class BuildAccountVerificationBanner extends StatelessWidget {
  final ThemeData  theme;
  final bool isVerifphone;
  final bool isVerifEmail;
  const BuildAccountVerificationBanner({
    super.key, required this.theme, required this.isVerifphone, required this.isVerifEmail,
  });

  @override
  Widget build(BuildContext context) {
    if (isVerifEmail ) {
      return const SizedBox.shrink();
    }
    return Container(
      color: Colors.orange.shade100,
      padding:  EdgeInsets.symmetric(
        vertical: 8.w,
        horizontal: 16.h,
      ),
      child: Row(
        children: [
         AppIcons.accountVerification,
          8.verticalSpace,
          Expanded(
            child: Text(
             AppStrings.accountVerificationMessage ,
              maxLines: AppStrings.accountVerificationMessage.length,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: AppColors.orange,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
