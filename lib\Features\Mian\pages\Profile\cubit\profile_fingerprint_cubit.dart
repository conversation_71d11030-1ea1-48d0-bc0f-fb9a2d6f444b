
import 'package:bloc/bloc.dart';
import 'package:local_auth/local_auth.dart';
import 'package:meta/meta.dart';

import '../../../../../Core/Services/fingerprint_service.dart';
import '../../../../../Core/Storage/Firebase/firebase_service.dart';
import '../../../../../Core/Storage/Local/local_storage_service.dart';
import '../../../../../Core/Storage/Local/local_storage_keys.dart';

part 'profile_fingerprint_state.dart';

class ProfileFingerprintCubit extends Cubit<ProfileFingerprintState> {
  ProfileFingerprintCubit() : super(ProfileFingerprintState()) {
    _init();
  }

  final FingerprintService _fingerprintService = FingerprintService();
  final FirebaseServices _firebaseService = FirebaseServices.instance;

  Future<void> _init() async {
    await checkBiometricAvailability();
    await _checkIfFingerprintAdded();
  }

  /// Check if biometric authentication is available
  Future<void> checkBiometricAvailability() async {
    emit(state.copyWith(isLoading: true));

    try {
      final bool isAvailable = await _fingerprintService.isBiometricAvailable();
      final List<BiometricType> availableBiometrics =
          await _fingerprintService.getAvailableBiometrics();

      emit(
        state.copyWith(
          isLoading: false,
          isBiometricAvailable: isAvailable,
          availableBiometrics: availableBiometrics,
          errorMessage: null,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          isBiometricAvailable: false,
          errorMessage: 'Failed to check biometric availability: $e',
        ),
      );
    }
  }

  /// Check if user has already added fingerprint
  Future<void> _checkIfFingerprintAdded() async {
    try {
      final String? savedHash =
          await _fingerprintService.getSavedFingerprintHash();

      emit(state.copyWith(hasFingerprintAdded: savedHash != null));
    } catch (e) {
      // If there's an error checking, assume not added
      emit(state.copyWith(hasFingerprintAdded: false));
    }
  }

  /// Add fingerprint to current user
  Future<void> addFingerprintToProfile() async {
    if (!state.isBiometricAvailable) {
      emit(
        state.copyWith(
          errorMessage: 'Biometric authentication is not available',
        ),
      );
      return;
    }

    emit(
      state.copyWith(isLoading: true, errorMessage: null, successMessage: null),
    );

    try {
      // Get current user ID
      final String? userId = LocalStorageService.getValue(
        LocalStorageKeys.token,
      );
      if (userId == null) {
        emit(
          state.copyWith(isLoading: false, errorMessage: 'User not logged in'),
        );
        return;
      }

      // Step 1: Authenticate and generate fingerprint hash
      final fingerprintResponse =
          await _fingerprintService.authenticateAndGenerateFingerprint();

      // log(fingerprintResponse.data);

      if (!fingerprintResponse.status) {
        emit(
          state.copyWith(
            isLoading: false,
            errorMessage: fingerprintResponse.message,
          ),
        );
        return;
      }

      final String fingerprintHash = fingerprintResponse.data as String;

      // Step 2: Check if fingerprint already exists
      final checkResponse = await _firebaseService.checkFingerprintExists(
        fingerprintHash,
      );
      if (checkResponse.status) {
        final Map<String, dynamic> checkData =
            checkResponse.data as Map<String, dynamic>;
        if (checkData['exists'] == true) {
          emit(
            state.copyWith(
              isLoading: false,
              errorMessage:
                  'This fingerprint is already registered with another account',
            ),
          );
          return;
        }
      }

      // Step 3: Add fingerprint to Firebase lock collection
      final addResponse = await _firebaseService.addFingerprintToUser(
        userId: userId,
        fingerprintHash: fingerprintHash,
      );

      if (!addResponse.status) {
        emit(
          state.copyWith(isLoading: false, errorMessage: addResponse.message),
        );
        return;
      }

      // Step 4: Save fingerprint data locally
      final saveResponse = await _fingerprintService.saveFingerprintLocally(
        fingerprintHash,
        userId,
      );

      if (!saveResponse.status) {
        emit(
          state.copyWith(
            isLoading: false,
            errorMessage:
                'Fingerprint added to server but failed to save locally: ${saveResponse.message}',
          ),
        );
        return;
      }

      emit(
        state.copyWith(
          isLoading: false,
          hasFingerprintAdded: true,
          successMessage:
              'Fingerprint added successfully! You can now use it to login.',
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: 'Failed to add fingerprint: $e',
        ),
      );
    }
  }

  /// Remove fingerprint from current user
  Future<void> removeFingerprintFromProfile() async {
    emit(
      state.copyWith(isLoading: true, errorMessage: null, successMessage: null),
    );

    try {
      // Get current user ID and saved fingerprint hash
      final String? userId = LocalStorageService.getValue(
        LocalStorageKeys.token,
      );
      final String? fingerprintHash =
          await _fingerprintService.getSavedFingerprintHash();

      if (userId == null) {
        emit(
          state.copyWith(isLoading: false, errorMessage: 'User not logged in'),
        );
        return;
      }

      if (fingerprintHash == null) {
        emit(
          state.copyWith(
            isLoading: false,
            errorMessage: 'No fingerprint found to remove',
          ),
        );
        return;
      }

      // Step 1: Remove from Firebase lock collection
      final removeResponse = await _firebaseService.removeFingerprintFromUser(
        userId: userId,
        fingerprintHash: fingerprintHash,
      );

      if (!removeResponse.status) {
        emit(
          state.copyWith(
            isLoading: false,
            errorMessage: removeResponse.message,
          ),
        );
        return;
      }

      // Step 2: Clear local fingerprint data
      final clearResponse = await _fingerprintService.clearFingerprintData();

      if (!clearResponse.status) {
        emit(
          state.copyWith(
            isLoading: false,
            errorMessage:
                'Removed from server but failed to clear locally: ${clearResponse.message}',
          ),
        );
        return;
      }

      emit(
        state.copyWith(
          isLoading: false,
          hasFingerprintAdded: false,
          successMessage: 'Fingerprint removed successfully',
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: 'Failed to remove fingerprint: $e',
        ),
      );
    }
  }

  /// Clear messages
  void clearMessages() {
    emit(state.copyWith(errorMessage: null, successMessage: null));
  }
}
