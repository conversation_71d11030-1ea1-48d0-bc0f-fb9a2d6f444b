part of 'profile_fingerprint_cubit.dart';

@immutable
class ProfileFingerprintState {
  final bool isLoading;
  final bool isBiometricAvailable;
  final bool hasFingerprintAdded;
  final List<BiometricType> availableBiometrics;
  final String? errorMessage;
  final String? successMessage;

  const ProfileFingerprintState({
    this.isLoading = false,
    this.isBiometricAvailable = false,
    this.hasFingerprintAdded = false,
    this.availableBiometrics = const [],
    this.errorMessage,
    this.successMessage,
  });

  ProfileFingerprintState copyWith({
    bool? isLoading,
    bool? isBiometricAvailable,
    bool? hasFingerprintAdded,
    List<BiometricType>? availableBiometrics,
    String? errorMessage,
    String? successMessage,
  }) {
    return ProfileFingerprintState(
      isLoading: isLoading ?? this.isLoading,
      isBiometricAvailable: isBiometricAvailable ?? this.isBiometricAvailable,
      hasFingerprintAdded: hasFingerprintAdded ?? this.hasFingerprintAdded,
      availableBiometrics: availableBiometrics ?? this.availableBiometrics,
      errorMessage: errorMessage,
      successMessage: successMessage,
    );
  }

  @override
  String toString() {
    return 'ProfileFingerprintState('
        'isLoading: $isLoading, '
        'isBiometricAvailable: $isBiometricAvailable, '
        'hasFingerprintAdded: $hasFingerprintAdded, '
        'availableBiometrics: $availableBiometrics, '
        'errorMessage: $errorMessage, '
        'successMessage: $successMessage'
        ')';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is ProfileFingerprintState &&
        other.isLoading == isLoading &&
        other.isBiometricAvailable == isBiometricAvailable &&
        other.hasFingerprintAdded == hasFingerprintAdded &&
        other.availableBiometrics == availableBiometrics &&
        other.errorMessage == errorMessage &&
        other.successMessage == successMessage;
  }

  @override
  int get hashCode {
    return isLoading.hashCode ^
        isBiometricAvailable.hashCode ^
        hasFingerprintAdded.hashCode ^
        availableBiometrics.hashCode ^
        errorMessage.hashCode ^
        successMessage.hashCode;
  }
}
