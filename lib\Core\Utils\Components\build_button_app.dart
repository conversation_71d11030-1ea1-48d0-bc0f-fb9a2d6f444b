import 'package:flutter/material.dart';
import 'build_text.dart';
import '../../Resources/app_colors.dart';
import '../../Resources/fonts.dart';

class BuildButtonApp extends StatelessWidget {
  final VoidCallback onPressed;
  final String text;

  const BuildButtonApp({
    super.key,
    required this.onPressed,
    required this.text,
  });
  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        padding: const EdgeInsets.symmetric(vertical: 12.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.0),
        ),
        elevation: 4,
      ),
      child: buildText(
        text: text,
        style: AppTextStyles.bodyLargeSemiBold(),
        textAlign: TextAlign.center,
      ),
    );
  }
} 

