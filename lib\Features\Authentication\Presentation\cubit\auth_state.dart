part of 'auth_cubit.dart';

@immutable
class AuthState {
  final bool loadingLogin;
  final bool loadingCreateAccount;
  final bool? statusLogin;
  final bool? statusSingUp;
  final String message;

  const AuthState({
    this.loadingLogin = false,
    this.loadingCreateAccount = false,
    this.statusLogin,
    this.statusSingUp,
    this.message = '',
  });

  AuthState copyWith({
    bool? loadingLogin,
    bool? statusSingUp,
    bool? loadingCreateAccount,
    bool? statusLogin,
    String? message,
  }) {
    return AuthState(
      loadingLogin: loadingLogin ?? this.loadingLogin,
      loadingCreateAccount: loadingCreateAccount ?? this.loadingCreateAccount,
      statusLogin: statusLogin ?? this.statusLogin,
      statusSingUp: statusSingUp ?? this.statusSingUp,
      message: message ?? this.message,
    );
  }
}
