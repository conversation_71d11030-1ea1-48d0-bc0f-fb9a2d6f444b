part of 'auth_cubit.dart';

@immutable
class AuthState {
  final bool loadingLogin;
  final bool loadingCreateAccount;
  final bool? status;
  final String message;
  
  const AuthState({
    this.loadingLogin = false,
    this.loadingCreateAccount = false,
    this.status ,
    this.message = '',
  });

  AuthState copyWith({
    bool? loadingLogin,
    bool? loadingCreateAccount,
    bool? status,
    String? message,
  }) {
    return AuthState(
      loadingLogin: loadingLogin ?? this.loadingLogin,
      loadingCreateAccount: loadingCreateAccount ?? this.loadingCreateAccount,
      status: status ?? this.status,
      message: message ?? this.message,
    );
  }
}
