import 'dart:developer';
import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';

import '../../../Features/Authentication/Data/Models/create_account.dart';
import 'collection_data.dart';
import 'response_model.dart';
import 'failure_model.dart';

class FirebaseServices {
  FirebaseServices._(); // Private constructor for singleton

  static final FirebaseServices _instance = FirebaseServices._();

  static FirebaseServices get instance => _instance;

  FirebaseAuth? _auth;
  FirebaseFirestore? _firestore;
  FirebaseStorage? _storage;
  bool _isInitialized = false;

  Future<void> initialize() async {
    if (!_isInitialized) {
      await Firebase.initializeApp();
      _auth = FirebaseAuth.instance;
      _firestore = FirebaseFirestore.instance;
      _storage = FirebaseStorage.instance;
      _isInitialized = true;
      // log('TestFIrebaseServices::: Firebase services initialized.');
    }
  }

  Future<ResponseModel> loginAccount(String email, String password) async {
    try {
      UserCredential userCredential = await _auth!.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      // log(
      //   'TestFIrebaseServices::: Successfully logged in user: ${userCredential.user?.uid}',
      // );
      // final data = await getUserData(
      //   (userCredential.user?.uid).toString(),
      //   AppCollectionData.users,
      // );
      // return data.copyWith(message: 'Login successfully');
      return ResponseModel.success(message: 'Login successfully',data: userCredential.user?.uid);
    } on FirebaseAuthException catch (signInError) {
      final failure = FailureModel(
        message: 'Firebase Authentication Error during sign in',
        error: signInError,
      );
      // log('TestFIrebaseServices::: Error signing in: ${failure.toString()}');
      return ResponseModel.error(message: failure.message, failure: failure);
    } catch (e) {
      log(e.toString());
      final failure = FailureModel(
        message: 'An unexpected error occurred during login',
        error: e,
      );
      log(
        'TestFIrebaseServices::: An unexpected error occurred during login: ${failure.toString()}',
      );
      
      return ResponseModel.error(message: failure.message, failure: failure);
    }
  }

  Future<ResponseModel> createAccount(CreateAccountModel account) async {
    try {
      UserCredential newUserCredential = await _auth!
          .createUserWithEmailAndPassword(
            email: account.email,
            password: account.password,
          );
      log(
        'TestFIrebaseServices::: Successfully created new user: ${newUserCredential.user?.uid}',
      );
      final userid = (newUserCredential.user?.uid).toString();

      final data = account.toJson(userid.toString());

      final response =await addData(AppCollectionData.users, userid, data);
      return response.copyWith(message: 'Account created successfully');
    } on FirebaseAuthException catch (createError) {
      final failure = FailureModel(
        message: 'Firebase Authentication Error during account creation',
        error: createError,
      );
      // log('TestFIrebaseServices::: Error creating user: ${failure.toString()}');
      return ResponseModel.error(message: failure.message, failure: failure);
    } catch (e) {
      final failure = FailureModel(
        message: 'An unexpected error occurred during account creation',
        error: e,
      );
      log(
        'TestFIrebaseServices::: An unexpected error occurred during account creation: ${failure.toString()}',
      );
      return ResponseModel.error(message: failure.message, failure: failure);
    }
  }

  Future<ResponseModel> addData(
    String collectionName,
    String documentId,
    Map<String, dynamic> data,
  ) async {
    try {
      await _firestore!.collection(collectionName).doc(documentId).set(data);
      // log(
      //   'TestFIrebaseServices::: Data successfully added to $collectionName/$documentId.',
      // );
      return ResponseModel.success(message: 'Data added successfully');
    } catch (e) {
      final failure = FailureModel(message: 'Error adding data', error: e);
      // log('TestFIrebaseServices::: Error adding data: ${failure.toString()}');
      return ResponseModel.error(message: failure.message, failure: failure);
    }
  }

  Future<ResponseModel> getUserData(
    String userId,
    String collectionName,
  ) async {
    try {
      final DocumentSnapshot<Map<String, dynamic>> documentSnapshot =
          await _firestore!.collection(collectionName).doc(userId).get();

      if (documentSnapshot.exists) {
        // log(
        //   'TestFIrebaseServices::: Successfully retrieved data for user ID: $userId from $collectionName.',
        // );
        return ResponseModel.success(
          message: 'User data retrieved successfully',
          data: documentSnapshot.data(),
        );
      } else {
        // log(
        //   'TestFIrebaseServices::: No data found for user ID: $userId in $collectionName.',
        // );
        return ResponseModel.error(
          message: 'No data found for this user',
          data: null,
        );
      }
    } catch (e) {
      final failure = FailureModel(
        message: 'Error getting user data',
        error: e,
      );
      // log(
      //   'TestFIrebaseServices::: Error getting data for user ID $userId from $collectionName: ${failure.toString()}',
      // );
      return ResponseModel.error(message: failure.message, failure: failure);
    }
  }

  Future<ResponseModel> getAllData(String collectionName) async {
    List<Map<String, dynamic>> results = [];
    try {
      QuerySnapshot<Map<String, dynamic>> querySnapshot =
          await _firestore!.collection(collectionName).get();
      for (QueryDocumentSnapshot<Map<String, dynamic>> doc
          in querySnapshot.docs) {
        results.add(doc.data());
      }
      log(
        'TestFIrebaseServices::: Successfully retrieved all data from $collectionName.',
      );
      return ResponseModel.success(
        message: 'Data retrieved successfully',
        data: results,
      );
    } catch (e) {
      final failure = FailureModel(message: 'Error getting data', error: e);
      log(
        'TestFIrebaseServices::: Error getting all data: ${failure.toString()}',
      );
      return ResponseModel.error(message: failure.message, failure: failure);
    }
  }

  Future<ResponseModel> uploadImage(File imageFile, String storagePath) async {
    try {
      final Reference storageReference = _storage!.ref().child(storagePath);
      final UploadTask uploadTask = storageReference.putFile(imageFile);
      final TaskSnapshot taskSnapshot = await uploadTask.whenComplete(() {});
      final String downloadURL = await taskSnapshot.ref.getDownloadURL();
      log(
        'TestFIrebaseServices::: Image uploaded to $storagePath, URL: $downloadURL',
      );
      return ResponseModel.success(
        message: 'Image uploaded successfully',
        data: downloadURL,
      );
    } catch (e) {
      final failure = FailureModel(message: 'Error uploading image', error: e);
      log(
        'TestFIrebaseServices::: Error uploading image: ${failure.toString()}',
      );
      return ResponseModel.error(message: failure.message, failure: failure);
    }
  }

  Future<ResponseModel> removeData(
    String collectionName,
    String documentId,
  ) async {
    try {
      await _firestore!.collection(collectionName).doc(documentId).delete();
      log(
        'TestFIrebaseServices:::Document $documentId removed from $collectionName',
      );
      return ResponseModel.success(message: 'Data removed successfully');
    } catch (e) {
      final failure = FailureModel(message: 'Error removing data', error: e);
      log('TestFIrebaseServices:::Error removing data: ${failure.toString()}');
      return ResponseModel.error(message: failure.message, failure: failure);
    }
  }

  Future<ResponseModel> updateData(
    String collectionName,
    String documentId,
    Map<String, dynamic> data,
  ) async {
    try {
      await _firestore!.collection(collectionName).doc(documentId).update(data);
      log(
        'TestFIrebaseServices:::Document $documentId in $collectionName updated with $data',
      );
      return ResponseModel.success(message: 'Data updated successfully');
    } catch (e) {
      final failure = FailureModel(message: 'Error updating data', error: e);
      log('TestFIrebaseServices:::Error updating data: ${failure.toString()}');
      return ResponseModel.error(message: failure.message, failure: failure);
    }
  }
}


// void main() async {
//   // Ensure Flutter is initialized
//   WidgetsFlutterBinding.ensureInitialized();

//   // Initialize Firebase services
//   await FirebaseServices.instance.initialize();

//   // Example usage:
//   const testEmail = '<EMAIL>';
//   const testPassword = 'securepassword';

//   final userId = await FirebaseServices.instance.loginOrCreateAccount(testEmail, testPassword);

//   if (userId != null) {
//    log('TestFIrebaseServices::: User ID: $userId');
//     final passwordData = {
//       'account_name': 'My Social Media',
//       'username': 'user123',
//       'password': 'mysocialpassword',
//     };
//     final success = await FirebaseServices.instance.addData('passwords', 'user_${userId}_social', passwordData);
//     if (success) {
//      log('TestFIrebaseServices::: Password data added successfully.');
//     } else {
//      log('TestFIrebaseServices::: Failed to add password data.');
//     }
//   } else {
//    log('TestFIrebaseServices::: Failed to login or create account.');
//   }
// }