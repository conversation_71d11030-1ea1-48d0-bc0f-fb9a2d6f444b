import 'dart:developer';
import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';

import '../../../Features/Authentication/Data/Models/create_account.dart';
import 'collection_data.dart';
import 'response_model.dart';
import 'failure_model.dart';

class FirebaseServices {
  FirebaseServices._(); // Private constructor for singleton

  static final FirebaseServices _instance = FirebaseServices._();

  static FirebaseServices get instance => _instance;

  FirebaseAuth? _auth;
  FirebaseFirestore? _firestore;
  FirebaseStorage? _storage;
  bool _isInitialized = false;

  Future<void> initialize() async {
    if (!_isInitialized) {
      await Firebase.initializeApp();
      _auth = FirebaseAuth.instance;
      _firestore = FirebaseFirestore.instance;
      _storage = FirebaseStorage.instance;
      _isInitialized = true;
      // log('TestFIrebaseServices::: Firebase services initialized.');
    }
  }

  Future<ResponseModel> loginAccount(String email, String password) async {
    try {
      UserCredential userCredential = await _auth!.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      // log(
      //   'TestFIrebaseServices::: Successfully logged in user: ${userCredential.user?.uid}',
      // );
      // final data = await getUserData(
      //   (userCredential.user?.uid).toString(),
      //   AppCollectionData.users,
      // );
      // return data.copyWith(message: 'Login successfully');
      return ResponseModel.success(
        message: 'Login successfully',
        data: userCredential.user?.uid,
      );
    } on FirebaseAuthException catch (signInError) {
      final failure = FailureModel(
        message: 'Firebase Authentication Error during sign in',
        error: signInError,
      );
      // log('TestFIrebaseServices::: Error signing in: ${failure.toString()}');
      return ResponseModel.error(message: failure.message, failure: failure);
    } catch (e) {
      log(e.toString());
      final failure = FailureModel(
        message: 'An unexpected error occurred during login',
        error: e,
      );
      // log(
      //   'TestFIrebaseServices::: An unexpected error occurred during login: ${failure.toString()}',
      // );

      return ResponseModel.error(message: failure.message, failure: failure);
    }
  }

  Future<ResponseModel> createAccount(CreateAccountModel account) async {
    try {
      UserCredential newUserCredential = await _auth!
          .createUserWithEmailAndPassword(
            email: account.email,
            password: account.password,
          );
      log(
        'TestFIrebaseServices::: Successfully created new user: ${newUserCredential.user?.uid}',
      );
      final userid = (newUserCredential.user?.uid).toString();

      final data = account.toJson(userid.toString());

      final response = await addData(AppCollectionData.users, userid, data);
      return response.copyWith(message: 'Account created successfully');
    } on FirebaseAuthException catch (createError) {
      final failure = FailureModel(
        message: 'Firebase Authentication Error during account creation',
        error: createError,
      );
      // log('TestFIrebaseServices::: Error creating user: ${failure.toString()}');
      return ResponseModel.error(message: failure.message, failure: failure);
    } catch (e) {
      final failure = FailureModel(
        message: 'An unexpected error occurred during account creation',
        error: e,
      );
      log(
        'TestFIrebaseServices::: An unexpected error occurred during account creation: ${failure.toString()}',
      );
      return ResponseModel.error(message: failure.message, failure: failure);
    }
  }

  Future<ResponseModel> addData(
    String collectionName,
    String documentId,
    Map<String, dynamic> data,
  ) async {
    try {
      await _firestore!.collection(collectionName).doc(documentId).set(data);
      // log(
      //   'TestFIrebaseServices::: Data successfully added to $collectionName/$documentId.',
      // );
      return ResponseModel.success(
        message: 'Added successfully',
        data: documentId,
      );
    } catch (e) {
      final failure = FailureModel(message: 'Error adding data', error: e);
      // log('TestFIrebaseServices::: Error adding data: ${failure.toString()}');
      return ResponseModel.error(message: failure.message, failure: failure);
    }
  }

  Future<ResponseModel> getUserData(
    String userId,
    String collectionName,
  ) async {
    try {
      final DocumentSnapshot<Map<String, dynamic>> documentSnapshot =
          await _firestore!.collection(collectionName).doc(userId).get();

      if (documentSnapshot.exists) {
        // log(
        //   'TestFIrebaseServices::: Successfully retrieved data for user ID: $userId from $collectionName.',
        // );
        return ResponseModel.success(
          message: 'User data retrieved successfully',
          data: documentSnapshot.data(),
        );
      } else {
        // log(
        //   'TestFIrebaseServices::: No data found for user ID: $userId in $collectionName.',
        // );
        return ResponseModel.error(
          message: 'No data found for this user',
          data: null,
        );
      }
    } catch (e) {
      final failure = FailureModel(
        message: 'Error getting user data',
        error: e,
      );
      // log(
      //   'TestFIrebaseServices::: Error getting data for user ID $userId from $collectionName: ${failure.toString()}',
      // );
      return ResponseModel.error(message: failure.message, failure: failure);
    }
  }

  Future<ResponseModel> getAllData(String collectionName) async {
    List<Map<String, dynamic>> results = [];
    try {
      QuerySnapshot<Map<String, dynamic>> querySnapshot =
          await _firestore!.collection(collectionName).get();
      for (QueryDocumentSnapshot<Map<String, dynamic>> doc
          in querySnapshot.docs) {
        results.add(doc.data());
      }
      log(
        'TestFIrebaseServices::: Successfully retrieved all data from $collectionName.',
      );
      return ResponseModel.success(
        message: 'Data retrieved successfully',
        data: results,
      );
    } catch (e) {
      final failure = FailureModel(message: 'Error getting data', error: e);
      log(
        'TestFIrebaseServices::: Error getting all data: ${failure.toString()}',
      );
      return ResponseModel.error(message: failure.message, failure: failure);
    }
  }

  Future<ResponseModel> uploadImage(File imageFile, String storagePath) async {
    try {
      final Reference storageReference = _storage!.ref().child(storagePath);
      final UploadTask uploadTask = storageReference.putFile(imageFile);
      final TaskSnapshot taskSnapshot = await uploadTask.whenComplete(() {});
      final String downloadURL = await taskSnapshot.ref.getDownloadURL();
      log(
        'TestFIrebaseServices::: Image uploaded to $storagePath, URL: $downloadURL',
      );
      return ResponseModel.success(
        message: 'Image uploaded successfully',
        data: downloadURL,
      );
    } catch (e) {
      final failure = FailureModel(message: 'Error uploading image', error: e);
      log(
        'TestFIrebaseServices::: Error uploading image: ${failure.toString()}',
      );
      return ResponseModel.error(message: failure.message, failure: failure);
    }
  }

  Future<ResponseModel> removeData(
    String collectionName,
    String documentId,
  ) async {
    try {
      await _firestore!.collection(collectionName).doc(documentId).delete();
      log(
        'TestFIrebaseServices:::Document $documentId removed from $collectionName',
      );
      return ResponseModel.success(message: 'Data removed successfully');
    } catch (e) {
      final failure = FailureModel(message: 'Error removing data', error: e);
      log('TestFIrebaseServices:::Error removing data: ${failure.toString()}');
      return ResponseModel.error(message: failure.message, failure: failure);
    }
  }

  Future<ResponseModel> updateData(
    String collectionName,
    String documentId,
    dynamic data,
  ) async {
    try {
      await _firestore!.collection(collectionName).doc(documentId).update(data);
      log(
        'TestFIrebaseServices:::Document $documentId in $collectionName updated with $data',
      );
      return ResponseModel.success(message: 'updated successfully');
    } catch (e) {
      final failure = FailureModel(message: 'Error updating data', error: e);
      log('TestFIrebaseServices:::Error updating data: ${failure.toString()}');
      return ResponseModel.error(message: failure.message, failure: failure);
    }
  }

  Future<ResponseModel> sendEmailVerification() async {
    try {
      final user = _auth!.currentUser;
      if (user != null && !user.emailVerified) {
        await user.sendEmailVerification();
        return ResponseModel.success(message: 'Verification email sent.');
      } else if (user != null && user.emailVerified) {
        return ResponseModel.success(message: 'Email is already verified.');
      } else {
        return ResponseModel.error(message: 'No user signed in.');
      }
    } on FirebaseAuthException catch (e) {
      final failure = FailureModel(
        message: 'Error sending verification email',
        error: e,
      );
      log(
        'TestFIrebaseServices:::Error sending verification email: ${failure.toString()}',
      );
      return ResponseModel.error(message: failure.message, failure: failure);
    } catch (e) {
      final failure = FailureModel(
        message: 'Unexpected error sending verification email',
        error: e,
      );
      log(
        'TestFIrebaseServices:::Unexpected error sending verification email: ${failure.toString()}',
      );
      return ResponseModel.error(message: failure.message, failure: failure);
    }
  }

  Future<ResponseModel> isEmailVerified() async {
    final user = _auth!.currentUser;
    if (user != null) {
      await user.reload(); // Ensure we have the latest verification status
      return ResponseModel.success(
        message: 'Checked email verification status.',
        data: user.emailVerified,
      );
    } else {
      return ResponseModel.error(message: 'No user signed in.', data: false);
    }
  }

  // For phone verification, you'll typically use a different flow involving OTP.
  // This simplified example just returns a success message as the actual implementation
  // involves UI interaction and state management.
  Future<ResponseModel> sendPhoneVerification(String phoneNumber) async {
    // In a real application, you would use FirebaseAuth's `verifyPhoneNumber` method.
    // This requires handling callbacks for code sent, auto-verification, and errors.
    // For simplicity in this service class, we'll just simulate success.
    log(
      'TestFIrebaseServices:::Requesting phone verification for: $phoneNumber',
    );
    return ResponseModel.success(
      message: 'Phone verification initiated. Implement UI flow for OTP.',
    );
  }

  /// Register user with fingerprint
  Future<ResponseModel> registerUserWithFingerprint({
    required String name,
    required String fingerprintHash,
    String? email,
    String? phoneNumber,
  }) async {
    try {
      // Create anonymous user first
      UserCredential userCredential = await _auth!.signInAnonymously();
      final String userId = userCredential.user!.uid;

      // Create user data with fingerprint
      final Map<String, dynamic> userData = {
        'user_id': userId,
        'name': name,
        'email': email,
        'phone_number': phoneNumber,
        'fingerprint_hash': fingerprintHash,
        'is_fingerprint_registered': true,
        'is_email_verified': false,
        'is_phone_verified': false,
        'created_at': FieldValue.serverTimestamp(),
        'updated_at': FieldValue.serverTimestamp(),
        'items': [],
        'image': '',
      };

      // Save to Firestore
      final response = await addData('users', userId, userData);
      if (response.status) {
        return ResponseModel.success(
          message: 'User registered with fingerprint successfully',
          data: userId,
        );
      } else {
        return response;
      }
    } on FirebaseAuthException catch (e) {
      final failure = FailureModel(
        message:
            'Firebase Authentication Error during fingerprint registration',
        error: e,
      );
      return ResponseModel.error(message: failure.message, failure: failure);
    } catch (e) {
      final failure = FailureModel(
        message: 'An unexpected error occurred during fingerprint registration',
        error: e,
      );
      return ResponseModel.error(message: failure.message, failure: failure);
    }
  }

  /// Login with fingerprint hash
  Future<ResponseModel> loginWithFingerprint(String fingerprintHash) async {
    try {
      // Search for user with this fingerprint hash
      final QuerySnapshot querySnapshot =
          await _firestore!
              .collection('users')
              .where('fingerprint_hash', isEqualTo: fingerprintHash)
              .limit(1)
              .get();

      if (querySnapshot.docs.isEmpty) {
        return ResponseModel.error(
          message: 'Fingerprint not recognized. Please register first.',
        );
      }

      final DocumentSnapshot userDoc = querySnapshot.docs.first;
      final String userId = userDoc.id;

      // Sign in anonymously (since we're using fingerprint instead of email/password)
      await _auth!.signInAnonymously();

      return ResponseModel.success(
        message: 'Login with fingerprint successful',
        data: userId,
      );
    } catch (e) {
      final failure = FailureModel(
        message: 'Error during fingerprint login',
        error: e,
      );
      return ResponseModel.error(message: failure.message, failure: failure);
    }
  }

  /// Update user fingerprint
  Future<ResponseModel> updateUserFingerprint({
    required String userId,
    required String newFingerprintHash,
  }) async {
    try {
      await _firestore!.collection('users').doc(userId).update({
        'fingerprint_hash': newFingerprintHash,
        'is_fingerprint_registered': true,
        'updated_at': FieldValue.serverTimestamp(),
      });

      return ResponseModel.success(message: 'Fingerprint updated successfully');
    } catch (e) {
      final failure = FailureModel(
        message: 'Error updating fingerprint',
        error: e,
      );
      return ResponseModel.error(message: failure.message, failure: failure);
    }
  }

  /// Check if fingerprint exists
  Future<ResponseModel> checkFingerprintExists(String fingerprintHash) async {
    try {
      final QuerySnapshot querySnapshot =
          await _firestore!
              .collection('users')
              .where('fingerprint_hash', isEqualTo: fingerprintHash)
              .limit(1)
              .get();

      final bool exists = querySnapshot.docs.isNotEmpty;
      String? userId;

      if (exists) {
        userId = querySnapshot.docs.first.id;
      }

      return ResponseModel.success(
        message: exists ? 'Fingerprint found' : 'Fingerprint not found',
        data: {'exists': exists, 'userId': userId},
      );
    } catch (e) {
      final failure = FailureModel(
        message: 'Error checking fingerprint',
        error: e,
      );
      return ResponseModel.error(message: failure.message, failure: failure);
    }
  }
}
