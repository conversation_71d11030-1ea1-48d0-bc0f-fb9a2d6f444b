import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'Components/show_message.dart';
import '../Resources/app_colors.dart';

class ClipboardUtils {
  static void copyToClipboard({
    required BuildContext context,
    required String text,
    String? message,
    Color? backgroundColor,
    Duration duration = const Duration(seconds: 2),
  }) {
    Clipboard.setData(ClipboardData(text: text));

    showMessageScafold(context,
      message: message ?? 'Password copied',
      backgroundColor: backgroundColor ?? AppColors.primary,
      duration: duration,
    );
  }
}
