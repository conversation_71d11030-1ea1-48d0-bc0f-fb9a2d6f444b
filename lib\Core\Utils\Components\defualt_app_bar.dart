import 'package:flutter/material.dart';
import 'build_text.dart';
import '../../Resources/fonts.dart';

class DefualtAppBar extends StatelessWidget implements PreferredSizeWidget {
  const DefualtAppBar({super.key, required this.text});

  final String text;

  @override
  Size get preferredSize => Size.fromHeight(kToolbarHeight);
  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: buildText(
        text: text,
        style: AppTextStyles.bodyLargeMedium(),
        textAlign: TextAlign.center,
      ),

      backgroundColor: Colors.transparent,
      elevation: 0,
      centerTitle: true,
    );
  }
}
