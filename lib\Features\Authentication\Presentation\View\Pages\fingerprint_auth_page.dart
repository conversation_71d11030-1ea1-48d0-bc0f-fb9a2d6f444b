import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:icons_plus/icons_plus.dart';

import '../../../../../Core/Utils/Components/show_message.dart';
import '../../../../Mian/pages/main_page.dart';
import '../../cubit/fingerprint_auth_cubit.dart';

class FingerprintAuthPage extends StatefulWidget {
  const FingerprintAuthPage({super.key});

  @override
  State<FingerprintAuthPage> createState() => _FingerprintAuthPageState();
}

class _FingerprintAuthPageState extends State<FingerprintAuthPage> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Fingerprint Authentication'),
        centerTitle: true,
      ),
      body: BlocConsumer<FingerprintAuthCubit, FingerprintAuthState>(
        listener: (context, state) {
          if (state.isLoading) {
            showMessageScafold(
              context,
              message: 'Processing...',
              type: MessageType.loading,
            );
          } else {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          }

          if (state.errorMessage != null) {
            showMessageScafold(
              context,
              message: state.errorMessage!,
              type: MessageType.error,
            );
          }

          if (state.successMessage != null) {
            showMessageScafold(
              context,
              message: state.successMessage!,
              type: MessageType.success,
            );
          }

          if (state.isAuthenticated) {
            // Navigate to main page
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => const MainPage()),
            );
          }
        },
        builder: (context, state) {
          return SingleChildScrollView(
            padding: EdgeInsets.all(24.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Header
                Container(
                  padding: EdgeInsets.all(32.w),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Bootstrap.fingerprint,
                        size: 80.sp,
                        color: Theme.of(context).primaryColor,
                      ),
                      SizedBox(height: 16.h),
                      Text(
                        'Secure Access with Fingerprint',
                        style: TextStyle(
                          fontSize: 20.sp,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        'Register or login using your fingerprint for secure access to your data',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 32.h),

                // Check if biometric is available
                if (!state.isBiometricAvailable) ...[
                  Container(
                    padding: EdgeInsets.all(16.w),
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12.r),
                      border: Border.all(color: Colors.orange),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Bootstrap.exclamation_triangle,
                          color: Colors.orange,
                          size: 24.sp,
                        ),
                        SizedBox(width: 12.w),
                        Expanded(
                          child: Text(
                            'Biometric authentication is not available on this device',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.orange[800],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 24.h),
                ],

                // Available biometric types
                if (state.availableBiometrics.isNotEmpty) ...[
                  Text(
                    'Available Authentication Methods:',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 12.h),
                  Wrap(
                    spacing: 8.w,
                    children:
                        state.availableBiometrics.map((type) {
                          return Chip(
                            label: Text(_getBiometricTypeName(type)),
                            avatar: Icon(
                              _getBiometricTypeIcon(type),
                              size: 18.sp,
                            ),
                          );
                        }).toList(),
                  ),
                  SizedBox(height: 24.h),
                ],

                // Registration form (only show if not registered)
                if (!state.isRegistered) ...[
                  Text(
                    'Registration Information',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 16.h),

                  // Name field
                  TextFormField(
                    controller: _nameController,
                    decoration: InputDecoration(
                      labelText: 'Full Name *',
                      prefixIcon: const Icon(Bootstrap.person),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                  ),
                  SizedBox(height: 16.h),

                  // Email field (optional)
                  TextFormField(
                    controller: _emailController,
                    decoration: InputDecoration(
                      labelText: 'Email (Optional)',
                      prefixIcon: const Icon(Bootstrap.envelope),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                    keyboardType: TextInputType.emailAddress,
                  ),
                  SizedBox(height: 16.h),

                  // Phone field (optional)
                  TextFormField(
                    controller: _phoneController,
                    decoration: InputDecoration(
                      labelText: 'Phone Number (Optional)',
                      prefixIcon: const Icon(Bootstrap.telephone),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                    keyboardType: TextInputType.phone,
                  ),
                  SizedBox(height: 24.h),

                  // Register button
                  ElevatedButton.icon(
                    onPressed:
                        state.isBiometricAvailable && !state.isLoading
                            ? () => _registerWithFingerprint(context)
                            : null,
                    icon: const Icon(Bootstrap.fingerprint),
                    label: const Text('Register with Fingerprint'),
                    style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                  ),
                ] else ...[
                  // Login section (if already registered)
                  Text(
                    'Welcome Back!',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 16.h),

                  Text(
                    'Use your fingerprint to access your account',
                    style: TextStyle(fontSize: 14.sp, color: Colors.grey[600]),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 24.h),

                  // Login button
                  ElevatedButton.icon(
                    onPressed:
                        state.isBiometricAvailable && !state.isLoading
                            ? () =>
                                context
                                    .read<FingerprintAuthCubit>()
                                    .loginWithFingerprint()
                            : null,
                    icon: const Icon(Bootstrap.fingerprint),
                    label: const Text('Login with Fingerprint'),
                    style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                  ),
                  SizedBox(height: 16.h),

                  // Clear data button
                  TextButton.icon(
                    onPressed:
                        !state.isLoading
                            ? () => _showClearDataDialog(context)
                            : null,
                    icon: const Icon(Bootstrap.trash),
                    label: const Text('Clear Fingerprint Data'),
                    style: TextButton.styleFrom(foregroundColor: Colors.red),
                  ),
                ],

                SizedBox(height: 32.h),

                // Check biometric availability button
                OutlinedButton.icon(
                  onPressed:
                      !state.isLoading
                          ? () =>
                              context
                                  .read<FingerprintAuthCubit>()
                                  .checkBiometricAvailability()
                          : null,
                  icon: const Icon(Bootstrap.arrow_clockwise),
                  label: const Text('Check Biometric Availability'),
                  style: OutlinedButton.styleFrom(
                    padding: EdgeInsets.symmetric(vertical: 12.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  void _registerWithFingerprint(BuildContext context) {
    final String name = _nameController.text.trim();
    if (name.isEmpty) {
      showMessageScafold(
        context,
        message: 'Please enter your name',
        type: MessageType.error,
      );
      return;
    }

    context.read<FingerprintAuthCubit>().registerWithFingerprint(
      name: name,
      email:
          _emailController.text.trim().isEmpty
              ? null
              : _emailController.text.trim(),
      phoneNumber:
          _phoneController.text.trim().isEmpty
              ? null
              : _phoneController.text.trim(),
    );
  }

  void _showClearDataDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Clear Fingerprint Data'),
            content: const Text(
              'Are you sure you want to clear all fingerprint data? You will need to register again.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  context.read<FingerprintAuthCubit>().clearFingerprintData();
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Clear'),
              ),
            ],
          ),
    );
  }

  String _getBiometricTypeName(dynamic type) {
    // This is a simplified mapping. You might need to adjust based on the actual BiometricType enum
    switch (type.toString()) {
      case 'BiometricType.fingerprint':
        return 'Fingerprint';
      case 'BiometricType.face':
        return 'Face ID';
      case 'BiometricType.iris':
        return 'Iris';
      default:
        return 'Biometric';
    }
  }

  IconData _getBiometricTypeIcon(dynamic type) {
    switch (type.toString()) {
      case 'BiometricType.fingerprint':
        return Bootstrap.fingerprint;
      case 'BiometricType.face':
        return Bootstrap.person_circle;
      case 'BiometricType.iris':
        return Bootstrap.eye;
      default:
        return Bootstrap.shield_check;
    }
  }
}
