import 'package:flutter/material.dart';
import 'package:icons_plus/icons_plus.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  // Mock data - in a real app, this would come from your authentication service
  final String _name = "<PERSON>";
  final String _email = "<EMAIL>";
  final String _phone = "+****************";
  final bool _isEmailVerified = false;
  final bool _isPhoneVerified = true;
  final bool _isBiometricAvailable = true;
  final bool _isBiometricEnabled = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Account verification banner
              if (!_isEmailVerified)
                Container(
                  color: Colors.orange.shade100,
                  padding: const EdgeInsets.symmetric(
                    vertical: 8,
                    horizontal: 16,
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Bootstrap.shield_exclamation,
                        color: Colors.orange.shade800,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Your account is not fully verified. Verify your email for better security.',
                          style: TextStyle(
                            color: Colors.orange.shade800,
                            fontSize: 13,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
        
              // Profile header with photo
              Container(
                color: theme.primaryColor,
                padding: const EdgeInsets.only(bottom: 24, left: 20, right: 20),
                child: Column(
                  children: [
                    const SizedBox(height: 16),
                    // Profile photo
                    Stack(
                      alignment: Alignment.bottomRight,
                      children: [
                        CircleAvatar(
                          radius: 50,
                          backgroundColor: Colors.white.withAlpha(50),
                          child: Text(
                            _name.split(' ').map((e) => e[0]).join(''),
                            style: const TextStyle(
                              fontSize: 36,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: theme.primaryColor,
                              width: 2,
                            ),
                          ),
                          child: Icon(
                            Bootstrap.camera,
                            size: 18,
                            color: theme.primaryColor,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    // Name
                    Text(
                      _name,
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
        
              // Profile information section
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Section title
                    Text(
                      'Personal Information',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
        
                    // Email
                    _buildInfoItem(
                      icon: Bootstrap.envelope,
                      title: 'Email',
                      value: _email,
                      isVerified: _isEmailVerified,
                      onVerify: () => _verifyEmail(),
                    ),
        
                    const Divider(),
        
                    // Phone
                    _buildInfoItem(
                      icon: Bootstrap.phone,
                      title: 'Phone',
                      value: _phone,
                      isVerified: _isPhoneVerified,
                      onVerify: () => _verifyPhone(),
                    ),
        
                    const SizedBox(height: 32),
        
                    // Security section
                    Text(
                      'Security',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
        
                    // Change password
                    _buildSecurityItem(
                      icon: Bootstrap.key,
                      title: 'Change Password',
                      onTap: () => _changePassword(),
                    ),
        
                    const Divider(),
        
                    // Biometric authentication
                    _buildSecurityToggleItem(
                      icon: Bootstrap.fingerprint,
                      title: 'Biometric Authentication',
                      subtitle: 'Use fingerprint or face ID to login',
                      isEnabled: _isBiometricEnabled,
                      isAvailable: _isBiometricAvailable,
                      onChanged: (value) => _toggleBiometric(value),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String title,
    required String value,
    required bool isVerified,
    required VoidCallback onVerify,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                ),
                const SizedBox(height: 4),
                Text(value, style: const TextStyle(fontSize: 16)),
                if (!isVerified) ...[
                  const SizedBox(height: 8),
                  ElevatedButton.icon(
                    onPressed: onVerify,
                    icon: const Icon(Bootstrap.check_circle, size: 16),
                    label: const Text('Verify'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      textStyle: const TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (isVerified)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Bootstrap.check_circle,
                    size: 12,
                    color: Colors.green.shade700,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Verified',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.green.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSecurityItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            Icon(icon, size: 20, color: Colors.grey[600]),
            const SizedBox(width: 16),
            Expanded(child: Text(title, style: const TextStyle(fontSize: 16))),
            Icon(Bootstrap.chevron_right, size: 16, color: Colors.grey[400]),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityToggleItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool isEnabled,
    required bool isAvailable,
    required Function(bool) onChanged,
  }) {
    return Opacity(
      opacity: isAvailable ? 1.0 : 0.5,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            Icon(icon, size: 20, color: Colors.grey[600]),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(title, style: const TextStyle(fontSize: 16)),
                  Text(
                    subtitle,
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                  if (!isAvailable)
                    Text(
                      'Not available on this device',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.red[400],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                ],
              ),
            ),
            Switch(
              value: isEnabled && isAvailable,
              onChanged: isAvailable ? onChanged : null,
              activeColor: Theme.of(context).primaryColor,
            ),
          ],
        ),
      ),
    );
  }

  // Action methods
  void _verifyEmail() {
    // Implement email verification logic
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Verify Email'),
            content: const Text(
              'A verification link has been sent to your email address.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  void _verifyPhone() {
    // Implement phone verification logic
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Verify Phone'),
            content: const Text(
              'A verification code has been sent to your phone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  void _changePassword() {
    // Implement password change logic
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Change Password'),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  obscureText: true,
                  decoration: InputDecoration(labelText: 'Current Password'),
                ),
                SizedBox(height: 16),
                TextField(
                  obscureText: true,
                  decoration: InputDecoration(labelText: 'New Password'),
                ),
                SizedBox(height: 16),
                TextField(
                  obscureText: true,
                  decoration: InputDecoration(
                    labelText: 'Confirm New Password',
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Change'),
              ),
            ],
          ),
    );
  }

  void _toggleBiometric(bool value) {
    // Implement biometric toggle logic
    setState(() {
      // In a real app, you would save this preference
      // and configure biometric authentication
    });
  }
}
