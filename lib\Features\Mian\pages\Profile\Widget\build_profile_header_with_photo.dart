import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../Core/Models/user_model.dart';
import '../../../../../Core/Resources/app_colors.dart';
import '../../../../../Core/Resources/app_icons.dart';
import '../../../../../Core/Utils/Components/build_text.dart';
import '../../../cubit/main_cubit.dart';

class BuildProfileHeaderWithPhoto extends StatelessWidget {
  const BuildProfileHeaderWithPhoto({
    super.key,
    required this.theme,
    required this.user,
  });

  final ThemeData theme;
  final UserModel? user;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: theme.primaryColor,
      height: .25.sh,
      padding: const EdgeInsets.only(bottom: 24, left: 20, right: 20).r,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          16.verticalSpace,
          Stack(
            alignment: Alignment.bottomRight,
            children: [
              CircleAvatar(
                radius: 50.w,
                backgroundColor: Colors.white.withAlpha(50),
                child:  user!.image != null
                      ? Image.network(
                          user!.image!,
                          fit: BoxFit.contain,
                        )
                      : Text(
                 (user?.name ?? ".")
                      .trim()
                      .split(' ')
                      .map((e) => e[0])
                      .join('')
                      .toUpperCase(),
                  style: theme.textTheme.displaySmall?.copyWith(
                    color: Colors.white,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.all(4).w,
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                  border: Border.all(color: AppColors.primary, width: 2.w),
                ),
                child: GestureDetector(
                  onTap: () => context.read<MainCubit>().uploadImage(),
                  child: AppIcons.camera,
                ),
                // IconButton(child: AppIcons.camera),
              ),
            ],
          ),
          16.verticalSpace,
          // Name
          buildText(
            text: user?.name ?? "",
            style: theme.textTheme.headlineMedium!.copyWith(
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }
}
